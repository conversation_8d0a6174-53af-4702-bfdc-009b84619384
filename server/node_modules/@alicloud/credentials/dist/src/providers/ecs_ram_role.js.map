{"version": 3, "file": "ecs_ram_role.js", "sourceRoot": "", "sources": ["../../../src/providers/ecs_ram_role.ts"], "names": [], "mappings": ";;AACA,iCAA2C;AAC3C,uCAA0E;AAE1E,MAAM,aAAa,GAAG,EAAE,GAAG,EAAE,CAAC;AAC9B,MAAM,4BAA4B,GAAG,KAAK,CAAC,CAAC,UAAU;AAEtD,MAAqB,6BAA8B,SAAQ,mCAAyB;IAWlF,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,oCAAoC,EAAE,CAAC;IACpD,CAAC;IAED,YAAY,OAA6C;QACvD,KAAK,CAAC,oBAAU,EAAE,aAAa,CAAC,CAAC;QAXnC,WAAW;QACH,cAAS,GAAG,gBAAS,CAAC;QAW5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAG,OAAO,CAAC,4BAA4B,EAAE;YACvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,oCAAoC,EAAE,CAAC;SAC5D;IACH,CAAC;IAGD,oCAAoC;QAClC,OAAO,UAAU,CAAC,KAAK,IAAI,EAAE;YAC3B,IAAI;gBACF,IAAG,IAAI,CAAC,iBAAiB,EAAE;oBACzB,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;iBAC7B;aACF;YAAC,OAAM,GAAG,EAAE;gBACX,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,GAAG,CAAC,CAAC;aACnE;oBAAS;gBACR,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,oCAAoC,EAAE,CAAC;aAC5D;QACH,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;IAChB,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACxB,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,8CAA8C;QAC9C,MAAM,OAAO,GAAG,cAAO,CAAC,OAAO,EAAE;aAC9B,UAAU,CAAC,KAAK,CAAC;aACjB,YAAY,CAAC,MAAM,CAAC;aACpB,QAAQ,CAAC,iBAAiB,CAAC;aAC3B,QAAQ,CAAC,mBAAmB,CAAC;aAC7B,WAAW,CAAC;YACX,yCAAyC,EAAE,GAAG,4BAA4B,EAAE;SAC7E,CAAC;aACD,eAAe,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;aACzC,kBAAkB,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC;aAC/C,KAAK,EAAE,CAAC;QAEX,mCAAmC;QACnC,kCAAkC;QAClC,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC/C,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;gBAC/B,MAAM,IAAI,KAAK,CAAC,kCAAkC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;aAC1E;YACD,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SACvC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,MAAM,KAAK,CAAC;aACb;YACD,OAAO,IAAI,CAAC;SACb;IAEH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,MAAM,OAAO,GAAG,cAAO,CAAC,OAAO,EAAE;aAC9B,UAAU,CAAC,KAAK,CAAC;aACjB,YAAY,CAAC,MAAM,CAAC;aACpB,QAAQ,CAAC,iBAAiB,CAAC;aAC3B,QAAQ,CAAC,6CAA6C,CAAC;aACvD,eAAe,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;aACzC,kBAAkB,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC;QAEnD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACpD,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,OAAO,CAAC,WAAW,CAAC;gBAClB,6BAA6B,EAAE,aAAa;aAC7C,CAAC,CAAC;SACJ;QAED,mCAAmC;QACnC,gCAAgC;QAEhC,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,yBAAyB,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,EAAE,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;SAC7G;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;SACrC;QAED,MAAM,OAAO,GAAG,cAAO,CAAC,OAAO,EAAE;aAC9B,UAAU,CAAC,KAAK,CAAC;aACjB,YAAY,CAAC,MAAM,CAAC;aACpB,QAAQ,CAAC,iBAAiB,CAAC;aAC3B,QAAQ,CAAC,8CAA8C,QAAQ,EAAE,CAAC;aAClE,eAAe,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;aACzC,kBAAkB,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC;QAEnD,mCAAmC;QACnC,kCAAkC;QAClC,qCAAqC;QAErC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACpD,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,OAAO,CAAC,WAAW,CAAC;gBAClB,6BAA6B,EAAE,aAAa;aAC7C,CAAC,CAAC;SACJ;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,qCAAqC,QAAQ,CAAC,UAAU,eAAe,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;SACpH;QAED,IAAI,IAAI,CAAC;QACT,IAAI;YACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC7C;QAAC,OAAO,EAAE,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,4CAA4C,EAAE,CAAC,OAAO,EAAE,CAAC,CAAA;SAC1E;QAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAC9E,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;SACxC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAA;SAClE;QACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,OAAO,IAAI,iBAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAClG,CAAC;IAED,eAAe;QACb,OAAO,cAAc,CAAC;IACxB,CAAC;CACF;AAnKD,gDAmKC;AAED,MAAM,oCAAoC;IAOxC;QACE,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED,YAAY,CAAC,QAAgB;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAiB,CAAC,aAAsB;QACtC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe,CAAC,WAAmB;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kBAAkB,CAAC,cAAsB;QACvC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gCAAgC,CAAC,4BAAqC;QACpE,IAAI,CAAC,4BAA4B,GAAG,4BAA4B,CAAA;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK;QACH,oBAAoB;QACpB,IAAI,OAAO,CAAC,GAAG,CAAC,mCAAmC,IAAI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;YAC/H,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACjD;QAED,kBAAkB;QAClB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;SACxD;QAED,kBAAkB;QAClB,IAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;YACnH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC3B;QAED,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CAEF"}