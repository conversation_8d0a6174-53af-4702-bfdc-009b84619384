{"version": 3, "file": "static_sts.js", "sourceRoot": "", "sources": ["../../../src/providers/static_sts.ts"], "names": [], "mappings": ";;;;;;AACA,iEAAyC;AAGzC;;GAEG;AACH,MAAa,mCAAmC;IAKvC,eAAe,CAAC,WAAmB;QACxC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,mBAAmB,CAAC,eAAuB;QAChD,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,iBAAiB,CAAC,aAAqB;QAC5C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;SAC/D;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;SACvE;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;SAClE;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QAED,OAAO,IAAI,4BAA4B,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;CACF;AA/CD,kFA+CC;AAED;;GAEG;AACH,MAAqB,4BAA4B;IAC/C,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,mCAAmC,EAAE,CAAC;IACnD,CAAC;IAMD,YAAmB,OAA4C;QAC7D,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;IAC7C,CAAC;IAED,eAAe;QACb,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO,qBAAW,CAAC,OAAO,EAAE;aACzB,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC;aACjC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;aACzC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC;aACrC,gBAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IACb,CAAC;CACF;AA3BD,+CA2BC"}