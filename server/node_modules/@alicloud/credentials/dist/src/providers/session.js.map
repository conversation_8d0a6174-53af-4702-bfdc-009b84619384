{"version": 3, "file": "session.js", "sourceRoot": "", "sources": ["../../../src/providers/session.ts"], "names": [], "mappings": ";;;;;;AAAA,iCAAiC;AACjC,yCAA4C;AAE5C,iEAAwC;AAE3B,QAAA,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC;AAElC,MAAa,OAAO;IAMlB,YAAY,WAAmB,EAAE,eAAuB,EAAE,aAAqB,EAAE,UAAkB;QACjG,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CACF;AAZD,0BAYC;AAID,MAAa,yBAAyB;IAUpC,YAAY,YAAoB,CAAC,EAAE,eAAuB,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,kBAAU,CAAC;QACzC,IAAG,YAAY,EAAE;YACf,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;YACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,cAAc,GAAI,CAAC,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAEvC,OAAO,qBAAW,CAAC,OAAO,EAAE;aACrB,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;aACzC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;aACjD,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;aAC7C,gBAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,gBAAgB;QACd,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC;QAChE,IAAG,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;SAC3E;IACH,CAAC;IAED,qBAAqB;QACnB,MAAM,wBAAwB,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;QAClE,OAAO,wBAAwB,GAAG,EAAE,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC;IACvE,CAAC;IAED,UAAU,CAAC,IAAY,EAAE,WAAmB,EAAE,SAAiB;QAC7D,MAAM,WAAW,GAAG,SAAS,GAAG,WAAW,CAAC;QAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC;QACvE,OAAO,IAAI,GAAG,WAAW,GAAG,YAAY,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;YACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;YAC9B,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;YACtD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;YAChC,IAAI,CAAC,mBAAmB,GAAG,IAAA,eAAQ,EAAC,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;YAC/D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;YACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,iBAAiB;YACjB,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG,EAAE;gBAC7B,OAAO;aACR;YACD,yBAAyB;YACzB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,cAAc,GAAI,IAAI,CAAC,SAAS,CAAC,EAAE;gBACjD,IAAI,CAAC,mBAAmB,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;aACjD;YACD,oDAAoD;YACpD,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,cAAc,GAAI,IAAI,CAAC,SAAS,CAAC,EAAE;gBACjD,IAAG,mBAAmB,EAAE;oBACtB,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;oBAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAA,eAAQ,EAAC,UAAU,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;oBAClE,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO;iBACR;gBACD,MAAM,oBAAoB,GAAG,EAAE,GAAG,IAAA,oBAAY,EAAC,EAAE,CAAC,CAAC;gBACnD,IAAI,CAAC,mBAAmB,GAAG,GAAG,GAAG,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC;aACxE;SACF;QAAC,OAAM,GAAG,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,MAAM,GAAG,CAAC;aACX;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;YAC9B,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,EAAE;gBAC7B,OAAO;aACR;YACD,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;SACnG;IACH,CAAC;IACD,KAAK,CAAC,UAAU;QACZ,IAAI,IAAI,CAAC,oBAAoB,EAAE,IAAI,IAAI,CAAC,wBAAwB,EAAE,EAAE;YAClE,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5B,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,oBAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC9C,OAAO,IAAI,CAAC;SACb;QAED,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC;IACpD,CAAC;IAED,wBAAwB;QACtB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC;IAC7E,CAAC;IAED,eAAe;QACb,OAAO,SAAS,CAAC;IACnB,CAAC;CACJ;AApHD,8DAoHC"}