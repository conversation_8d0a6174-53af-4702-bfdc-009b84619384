{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../../../src/providers/http.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAE1B,MAAa,OAAO;IAalB,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,cAAc,EAAE,CAAC;IAC9B,CAAC;IAED,YAAY,OAAuB;QACjC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAC/C,CAAC;IAED,YAAY;QACV,IAAG,IAAI,CAAC,GAAG,EAAC;YACV,OAAO,IAAI,CAAC,GAAG,CAAC;SACjB;QACD,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACxD,IAAI,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YACxD,GAAG,IAAI,GAAG,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SAC1C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAzCD,0BAyCC;AAED,MAAa,cAAc;IAazB,KAAK;QACH,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;SACzB;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;SACjB;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACnB;QAED,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,UAAU,CAAC,MAAc;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,YAAY,CAAC,QAAgB;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,OAAkC;QAC5C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,OAAkC;QAC5C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,YAAY,CAAC,QAAmC;QAC9C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAAC,GAAW;QACjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe,CAAC,WAAmB;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kBAAkB,CAAC,cAAsB;QACvC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAnFD,wCAmFC;AAED,MAAa,QAAQ;IAKnB,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,eAAe,EAAE,CAAC;IAC/B,CAAC;IAED,YAAY,OAAwB;QAClC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC3B,CAAC;CACF;AAdD,4BAcC;AAED,MAAM,eAAe;IAKnB,cAAc,CAAC,UAAkB;QAC/B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,OAAkC;QAC5C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;QACE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACpB,CAAC;IAED,KAAK;QACH,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;CACF;AAED,SAAS,cAAc,CAAC,OAAkC;IACxD,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAClD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;KACpD;IACD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC;AAEM,KAAK,UAAU,SAAS,CAAC,GAAY;IAC1C,MAAM,GAAG,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;IAE/B,IAAI,IAAI,CAAC;IACT,IAAI,GAAG,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QACxD,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;YAChC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,mCAAmC,CAAC;SACnE;KACF;IAED,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACxC,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,WAAW,EAAE,GAAG,CAAC,WAAW;QAC5B,cAAc,EAAE,GAAG,CAAC,cAAc;KACnC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IACpD,OAAO,QAAQ,CAAC,OAAO,EAAE;SACtB,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC;SACnC,WAAW,CAAC,QAAQ,CAAC,OAAoC,CAAC;SAC1D,QAAQ,CAAC,YAAsB,CAAC;SAChC,KAAK,EAAE,CAAC;AACb,CAAC;AAzBD,8BAyBC"}