{"version": 3, "file": "oidc_role_arn.js", "sourceRoot": "", "sources": ["../../../src/providers/oidc_role_arn.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2BAA8B;AAC9B,+BAAiC;AAIjC,uCAA2E;AAC3E,qDAAuC;AACvC,iCAA4C;AAE5C,MAAM,aAAa,GAAG,IAAA,gBAAS,EAAC,aAAQ,CAAC,CAAC;AAE1C,MAAM,qCAAqC;IAazC,mBAAmB,CAAC,eAAuB;QACzC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,qBAAqB,CAAC,IAAY;QAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,CAAC,eAAuB;QACzC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,CAAC,eAAuB;QACzC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe,CAAC,WAAmB;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe,CAAC,QAAgB;QAC9B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU,CAAC,MAAc;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,aAAa,CAAC,SAAkB;QAC9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe,CAAC,WAAmB;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kBAAkB,CAAC,cAAsB;QACvC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK;QACH,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;SACpE;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;SACpE;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;SACpE;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;SACnD;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;SACnF;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,iFAAiF,CAAC,CAAC;SACpG;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;SACnG;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;SAC1D;QAED,IAAI,IAAI,CAAC,eAAe,GAAG,GAAG,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;SAC3F;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC;SACzD;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,WAAW,EAAE,KAAK,MAAM,IAAI,KAAK,CAAC;SACrJ;QAED,eAAe;QACf,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,CAAC,WAAW,eAAe,CAAA;iBAC9D;qBAAM;oBACL,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,WAAW,eAAe,CAAA;iBAC1D;aACF;iBAAM;gBAAE,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAA;aAAE;SACjD;QAED,OAAO,IAAI,8BAA8B,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;CACF;AAED,MAAqB,8BAA+B,SAAQ,mCAAyB;IAenF,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,qCAAqC,EAAE,CAAC;IACrD,CAAC;IAED,YAAY,OAA8C;QACxD,KAAK,CAAC,oBAAU,CAAC,CAAC;QAXZ,cAAS,GAAG,gBAAS,CAAC;QAY5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACnD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAC7C,gBAAgB;QAChB,IAAI,CAAC,SAAS,GAAG,gBAAS,CAAC;IAC7B,CAAC;IAED,eAAe;QACb,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,cAAO,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC;QAEjM,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpC,OAAO,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC;QAClC,OAAO,CAAC,QAAQ,CAAC,GAAG,oBAAoB,CAAC;QACzC,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;QAC3B,OAAO,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACzC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE7B,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;QACnD,QAAQ,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;QAClC,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QACnC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;SAClC;QAED,QAAQ,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,eAAe,CAAA;QAClD,QAAQ,CAAC,iBAAiB,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAExD,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE/B,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpC,cAAc;QACd,OAAO,CAAC,cAAc,CAAC,GAAG,mCAAmC,CAAC;QAC9D,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE7B,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,mCAAmC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;SACrF;QAED,IAAI,IAAI,CAAC;QACT,IAAI;YACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;SACnD;QAAC,OAAO,EAAE,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,mDAAmD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACtG;QAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACnD;QAED,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;QACrF,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe,IAAI,CAAC,aAAa,EAAE;YACtD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QAED,OAAO,IAAI,iBAAO,CAAC,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;IAC9E,CAAC;CACF;AA7FD,iDA6FC"}