{"version": 3, "file": "ram_role_arn.js", "sourceRoot": "", "sources": ["../../../src/providers/ram_role_arn.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA6B;AAC7B,kDAA0B;AAE1B,qDAAuC;AAIvC,iCAA4C;AAC5C,uCAA2E;AAE3E,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,MAAM,CAAC,CAAC;AAE1B,4BAA4B;AAC5B,0BAA0B;AAC1B,sBAAsB;AACtB,sBAAsB;AACtB,IAAI;AAEJ,MAAM,oCAAoC;IAaxC,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;SACjF;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAElH,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;SAC1G;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC;SACzD;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,WAAW,EAAE,KAAK,MAAM,IAAI,KAAK,CAAC;SACrJ;QAED,mBAAmB;QACnB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,kBAAkB;YAClB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;SAC5B;QAED,IAAI,IAAI,CAAC,eAAe,GAAG,GAAG,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;SAC3F;QAED,eAAe;QACf,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,CAAC,WAAW,eAAe,CAAA;iBAC9D;qBAAM;oBACL,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,WAAW,eAAe,CAAA;iBAC1D;aACF;iBAAM;gBAAE,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAA;aAAE;SACjD;QAED,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,uBAAuB,CAAC,mBAAwC;QAC9D,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe,CAAC,QAAgB;QAC9B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAA;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe,CAAC,QAAgB;QAC9B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAA;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,CAAC,eAAuB;QACzC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAA;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU,CAAC,MAAc;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,UAAkB;QAC/B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,CAAC,eAAuB;QACzC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAA;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,aAAa,CAAC,SAAkB;QAC9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe,CAAC,WAAmB;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kBAAkB,CAAC,cAAsB;QACvC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,SAAS,MAAM,CAAC,GAAW;IACzB,MAAM,MAAM,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAEvC,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;SAC/B,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;SACpB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;SACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;SACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED,MAAqB,6BAA8B,SAAQ,mCAAyB;IAgBlF,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,oCAAoC,EAAE,CAAC;IACpD,CAAC;IAED,YAAY,OAA6C;QACvD,KAAK,CAAC,oBAAU,CAAC,CAAC;QAVpB,gBAAgB;QACR,cAAS,GAAG,gBAAS,CAAC;QAU5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC7C,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;QACvD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAC/C,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC;QACpE,MAAM,MAAM,GAAG,MAAM,CAAC;QACtB,MAAM,OAAO,GAAG,cAAO,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC;QAEjM,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpC,OAAO,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC;QAClC,OAAO,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC;QACjC,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;QAC3B,OAAO,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACzC,OAAO,CAAC,iBAAiB,CAAC,GAAG,WAAW,CAAC;QACzC,OAAO,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;QACpC,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC7C,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC;QAEjD,IAAI,WAAW,CAAC,aAAa,EAAE;YAC7B,OAAO,CAAC,eAAe,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC;SACtD;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrC,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QACnC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;SAClC;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,QAAQ,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;SAC1C;QAED,QAAQ,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,eAAe,CAAA;QAClD,QAAQ,CAAC,iBAAiB,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACxD,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE/B,qBAAqB;QACrB,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClD,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;SACxB;QACD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACnD,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;SACxB;QAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAG,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACvE,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QAEhB,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAC7B,GAAG,CAAC,YAAY,CAAC,CAAC;QAClB,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,GAAG,GAAG,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,CAAW,CAAC;QACtE,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;QACjC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE7B,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpC,cAAc;QACd,OAAO,CAAC,cAAc,CAAC,GAAG,mCAAmC,CAAC;QAC9D,OAAO,CAAC,4BAA4B,CAAC,GAAG,WAAW,CAAC,YAAY,CAAA;QAChE,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE7B,2BAA2B;QAC3B,0DAA0D;QAC1D,oDAAoD;QACpD,wCAAwC;QACxC,KAAK;QAEL,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAEhC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE;YAC9B,IAAI,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE;gBACvG,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;gBACxD,MAAM,kBAAkB,GAAI,IAAI,CAAC,OAAkB,CAAC,KAAK,CAAC,oFAAoF,CAAC,MAAM,CAAC,CAAC;gBACvJ,GAAG,CAAC,uBAAuB,CAAC,CAAA;gBAC5B,GAAG,CAAC,YAAY,CAAC,CAAA;gBACjB,IAAI,IAAI,CAAC,IAAI,KAAK,uBAAuB,IAAI,kBAAkB,KAAK,YAAY,EAAE;oBAChF,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;iBACrD;aACF;YAED,MAAM,IAAI,KAAK,CAAC,iCAAiC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;SACnF;QAED,IAAI,IAAI,CAAC;QACT,IAAI;YACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;SACnD;QAAC,OAAO,EAAE,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,kDAAkD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACrG;QAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;SAC3E;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;YACzG,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAA;SAC1E;QAED,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;QACrF,OAAO,IAAI,iBAAO,CAAC,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;IAC9E,CAAC;IAED,eAAe;QACb,OAAO,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,EAAE,CAAC;IACtE,CAAC;CACF;AA3ID,gDA2IC"}