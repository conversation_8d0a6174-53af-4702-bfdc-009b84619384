{"version": 3, "file": "default.js", "sourceRoot": "", "sources": ["../../../src/providers/default.ts"], "names": [], "mappings": ";;;;;AAAA,iEAAyC;AAEzC,gEAA0D;AAC1D,kEAA2D;AAC3D,gDAA2D;AAC3D,oEAA6D;AAC7D,gDAA2C;AAC3C,wDAAmD;AAEnD,MAAqB,0BAA0B;IAG7C,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,iCAAiC,EAAE,CAAC;IACjD,CAAC;IAED,YAAY,OAA0C;QACpD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,qDAAqD;QACrD,IAAI;YACF,MAAM,WAAW,GAAG,aAAsC,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC;YAC7E,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAClC;QAAC,OAAO,EAAE,EAAE;YACX,SAAS;SACV;QAED,aAAa;QACb,IAAI;YACF,MAAM,YAAY,GAAG,uBAA8B,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC;YACtE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACnC;QAAC,OAAO,EAAE,EAAE;YACX,SAAS;SACV;QAED,2BAA2B;QAC3B,IAAI;YACF,MAAM,kBAAkB,GAAG,qBAA6B,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC;YAC3E,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACzC;QAAC,OAAO,EAAE,EAAE;YACX,SAAS;SACV;QAED,+BAA+B;QAC/B,IAAI;YACF,MAAM,eAAe,GAAG,iBAA0B,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC;YACrE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACtC;QAAC,OAAO,EAAE,EAAE;YACX,SAAS;SACV;QAED,WAAW;QACX,IAAI;YACF,MAAM,kBAAkB,GAAG,sBAA6B,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC,KAAK,EAAE,CAAC;YAChI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACzC;QAAC,OAAO,EAAE,EAAE;YACX,SAAS;SACV;QAED,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,GAAG,aAAsB,CAAC,OAAO,EAAE,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC,KAAK,EAAE,CAAC;YAC3H,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAClC;QACD,OAAO,EAAE,EAAE;YACT,SAAS;SACV;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC;YAC3D,OAAO,qBAAW,CAAC,OAAO,EAAE;iBACzB,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC;iBAClC,mBAAmB,CAAC,KAAK,CAAC,eAAe,CAAC;iBAC1C,iBAAiB,CAAC,KAAK,CAAC,aAAa,CAAC;iBACtC,gBAAgB,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,EAAE,CAAC;iBACxF,KAAK,EAAE,CAAC;SACZ;QAED,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACrC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;YACjC,IAAI,KAAK,CAAC;YACV,IAAI;gBACF,KAAK,GAAG,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;aACzC;YAAC,OAAO,EAAE,EAAE;gBACX,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChB,SAAS;aACV;YACD,IAAI,KAAK,EAAE;gBACT,OAAO,qBAAW,CAAC,OAAO,EAAE;qBACzB,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC;qBAClC,mBAAmB,CAAC,KAAK,CAAC,eAAe,CAAC;qBAC1C,iBAAiB,CAAC,KAAK,CAAC,aAAa,CAAC;qBACtC,gBAAgB,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,EAAE,CAAC;qBACxF,KAAK,EAAE,CAAC;aACZ;SACF;QAED,MAAM,IAAI,KAAK,CAAC,qEAAqE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACpG,OAAO,CAAC,CAAC,OAAO,CAAC;QACnB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC;IAED,eAAe;QACb,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAlGD,6CAkGC;AAED,MAAM,iCAAiC;IACrC,KAAK;QACH,OAAO,IAAI,0BAA0B,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;CACF"}