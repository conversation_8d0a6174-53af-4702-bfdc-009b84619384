{"version": 3, "file": "uri_credential.js", "sourceRoot": "", "sources": ["../../src/uri_credential.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAE1B,sDAA8B;AAE9B,8EAAqD;AAErD,MAAqB,aAAc,SAAQ,4BAAiB;IAK1D,YAAY,GAAW;QACrB,MAAM,IAAI,GAAG,IAAI,gBAAM,CAAC;YACtB,IAAI,EAAE,iBAAiB;YACvB,cAAc,EAAE,GAAG;SACpB,CAAC,CAAC;QAEH,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,GAAG,EAAE;YACR,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;SACnE;aAAM;YACL,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;SAC3B;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,8FAA8F,CAAC,CAAC;SACjH;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;QAChC,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAClH,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,2BAA2B,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;SAC9F;QACD,MAAM,IAAI,GAAG,CAAC,MAAM,eAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAW,CAAC;QAC5D,IAAI,IAAI,CAAC;QACT,IAAI;YACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACzB;QAAC,OAAO,EAAE,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,gDAAgD,IAAI,EAAE,CAAC,CAAC;SACpG;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,oBAAoB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;SAC7E;QAED,IAAI,CAAC,iBAAiB,GAAG;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAC;IACJ,CAAC;CACF;AAhDD,gCAgDC"}