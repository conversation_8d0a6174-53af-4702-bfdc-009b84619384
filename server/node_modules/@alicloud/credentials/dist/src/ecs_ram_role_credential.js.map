{"version": 3, "file": "ecs_ram_role_credential.js", "sourceRoot": "", "sources": ["../../src/ecs_ram_role_credential.ts"], "names": [], "mappings": ";;;;;AAAA,8EAAqD;AACrD,kDAA0B;AAE1B,sDAA8B;AAE9B,MAAM,iBAAiB,GAAG,mEAAmE,CAAC;AAC9F,MAAM,uBAAuB,GAAG,yCAAyC,CAAC;AAE1E,MAAqB,oBAAqB,SAAQ,4BAAiB;IAUjE,YAAY,WAAmB,EAAE,EAAE,UAAkC,EAAE,EAAE,eAAwB,KAAK,EAAE,wBAAgC,KAAK;QAC3I,MAAM,IAAI,GAAG,IAAI,gBAAM,CAAC;YACtB,IAAI,EAAE,cAAc;SACrB,CAAC,CAAC;QACH,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;QACnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,GAAW,EAAE,UAAkC,EAAE;QAC7D,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACnD,OAAO,CAAC,MAAM,eAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAW,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;YACxB,IAAI,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YACvE,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,OAAO,CAAC,uBAAuB,EAAE;gBAC5D,OAAO,EAAE;oBACP,yCAAyC,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE;iBAC3E;gBACD,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YACH,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;gBAC/B,MAAM,IAAI,KAAK,CAAC,2DAA2D,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;aACnG;YACD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;YACzB,OAAO,CAAC,MAAM,eAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAW,CAAC;SACvD;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACnD,OAAO,GAAG;gBACR,OAAO,EAAE;oBACP,6BAA6B,EAAE,IAAI,CAAC,aAAa;iBAClD;gBACD,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;aACpC,CAAA;SACF;QACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,GAAG,GAAG,iBAAiB,GAAG,QAAQ,CAAC;QACzC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,iBAAiB,GAAG;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;QAED,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAC/C,CAAC;IAED,aAAa;QACX,OAAO,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;IAChD,CAAC;CACF;AAlFD,uCAkFC"}