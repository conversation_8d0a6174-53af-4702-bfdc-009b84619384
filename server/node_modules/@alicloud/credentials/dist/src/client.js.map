{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../src/client.ts"], "names": [], "mappings": ";;;;;;AAEA,wFAA6D;AAC7D,wFAA8D;AAE9D,sDAA8B;AAgBX,iBAhBZ,gBAAM,CAgBY;AAfzB,0EAAiD;AAe/C,0BAfK,0BAAe,CAeL;AAZjB,sEAAgE;AAa9D,sCAbK,mBAA2B,CAaL;AAZ7B,wEAAkE;AAYnC,uCAZxB,oBAA4B,CAYwB;AAX3D,4EAAqE;AAWR,wCAXtD,sBAA6B,CAWsD;AAV1F,8EAAuE;AAWrE,yCAXK,uBAA8B,CAWL;AAVhC,4EAAqE;AAUnC,wCAV3B,sBAA6B,CAU2B;AAT/D,kEAA6D;AAOlC,qCAPpB,iBAA0B,CAOoB;AANrD,0DAAqD;AAQY,iCAR1D,aAAsB,CAQ0D;AAPvF,0EAAoE;AAQE,wCAR/D,qBAA6B,CAQ+D;AAPnG,kEAA6D;AAOnB,qCAPnC,iBAA0B,CAOmC;AANpE,0DAAqE;AAMnE,iDANK,aAAsC,CAML;AAGxC,MAAM,sBAAsB;IAI1B,YAAY,IAAY,EAAE,QAA6B;QACrD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,cAAc;QAClB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;QACzD,OAAO,WAAW,CAAC,WAAW,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,kBAAkB;QACtB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;QACzD,OAAO,WAAW,CAAC,eAAe,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,gBAAgB;QACpB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;QACzD,OAAO,WAAW,CAAC,aAAa,CAAC;IACnC,CAAC;IAED,cAAc;QACZ,OAAO;IACT,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;QACzD,OAAO,IAAI,0BAAe,CAAC;YACzB,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,eAAe,EAAE,WAAW,CAAC,eAAe;YAC5C,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,WAAW,EAAE,SAAS;YACtB,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE;YACpB,YAAY,EAAE,WAAW,CAAC,YAAY;SACvC,CAAC,CAAC;IACL,CAAC;CACF;AAED,SAAS,0BAA0B,CAAC,CAAM;IACxC,IAAI,CAAC,CAAC,EAAE;QACJ,OAAO,KAAK,CAAC;KAChB;IACD,OAAO,OAAO,CAAC,CAAC,cAAc,KAAK,UAAU,IAAI,OAAO,CAAC,CAAC,eAAe,KAAK,UAAU,CAAC;AAC3F,CAAC;AAED,MAAqB,UAAU;IAE7B,YAAY,SAAwB,IAAI,EAAE,WAAgE,IAAI;QAC5G,IAAI,0BAA0B,CAAC,QAAQ,CAAC,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAA+B,CAAC,CAAC;SAClD;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACzB;IACH,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IACnC,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;IACzC,CAAC;IAEO,IAAI,CAAC,MAAc,EAAE,QAA6B;QACxD,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,UAAU,GAAG,IAAI,sBAAsB,CAAC,QAAQ,CAAC,eAAe,EAAE,EAAE,QAAQ,CAAC,CAAC;YACnF,OAAO;SACR;QACD,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,UAAU,GAAG,IAAI,sBAAsB,CAAC,SAAS,EAAE,iBAA0B,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;YACtG,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACjD;QAED,QAAQ,MAAM,CAAC,IAAI,EAAE;YACrB,KAAK,YAAY;gBACf,IAAI,CAAC,UAAU,GAAG,IAAI,sBAAsB,CAAC,YAAY,EAAE,mBAA2B,CAAC,OAAO,EAAE;qBAC7F,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;qBACnC,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC;qBAC3C,KAAK,EAAE,CAAC,CAAC;gBACZ,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,CAAC,UAAU,GAAG,IAAI,sBAAsB,CAAC,KAAK,EAAE,oBAA4B,CAAC,OAAO,EAAE;qBACvF,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;qBACnC,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC;qBAC3C,iBAAiB,CAAC,MAAM,CAAC,aAAa,CAAC;qBACvC,KAAK,EAAE,CAAC,CAAC;gBACZ,MAAM;YACR,KAAK,cAAc;gBACjB,IAAI,CAAC,UAAU,GAAG,IAAI,sBAAsB,CAAC,cAAc,EAAE,sBAA6B,CAAC,OAAO,EAAE;qBACjG,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC;qBAC7B,iBAAiB,CAAC,MAAM,CAAC,aAAa,CAAC;qBACvC,gCAAgC,CAAC,MAAM,CAAC,4BAA4B,CAAC;qBACrE,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;qBAC/B,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC;qBACzC,KAAK,EAAE,CAAC,CAAC;gBACZ,MAAM;YACR,KAAK,cAAc;gBAAE;oBACnB,IAAI,mBAAwC,CAAC;oBAC7C,IAAI,MAAM,CAAC,aAAa,EAAE;wBACxB,mBAAmB,GAAG,oBAA4B,CAAC,OAAO,EAAE;6BACzD,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;6BACnC,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC;6BAC3C,iBAAiB,CAAC,MAAM,CAAC,aAAa,CAAC;6BACvC,KAAK,EAAE,CAAC;qBACZ;yBAAM;wBACL,mBAAmB,GAAG,mBAA2B,CAAC,OAAO,EAAE;6BACxD,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;6BACnC,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC;6BAC3C,KAAK,EAAE,CAAC;qBACZ;oBACD,IAAI,CAAC,UAAU,GAAG,IAAI,sBAAsB,CAAC,cAAc,EAAE,sBAA6B,CAAC,OAAO,EAAE;yBACjG,uBAAuB,CAAC,mBAAmB,CAAC;yBAC5C,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC;yBAC3B,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;yBACzB,mBAAmB,CAAC,MAAM,CAAC,qBAAqB,CAAC;yBACjD,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC;yBAC3C,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;yBAC/B,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC;yBACzC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC;yBAC/B,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;yBACnC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;yBACnC,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC;wBAClC,4BAA4B;yBAC3B,KAAK,EAAE,CAAC,CAAC;iBACb;gBACC,MAAM;YACR,KAAK,eAAe;gBAClB,IAAI,CAAC,UAAU,GAAG,IAAI,sBAAsB,CAAC,eAAe,EAAE,uBAA8B,CAAC,OAAO,EAAE;qBACnG,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC;qBAC3B,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC;qBAC3C,qBAAqB,CAAC,MAAM,CAAC,iBAAiB,CAAC;qBAC/C,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC;qBAC3C,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;qBACzB,mBAAmB,CAAC,MAAM,CAAC,qBAAqB,CAAC;qBACjD,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;qBACnC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;qBACnC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC;qBAC/B,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;qBAC/B,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC;qBACzC,KAAK,EAAE,CAAC,CAAC;gBACZ,MAAM;YACR,KAAK,cAAc;gBACjB,IAAI,CAAC,UAAU,GAAG,IAAI,iCAAoB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;gBACtF,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,UAAU,GAAG,IAAI,iCAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAChE,MAAM;YACR,KAAK,iBAAiB;gBACpB,IAAI,CAAC,UAAU,GAAG,IAAI,sBAAsB,CAAC,iBAAiB,EAAE,aAAsB,CAAC,OAAO,EAAE;qBAC7F,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC;qBACzC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;qBAC/B,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC;qBACzC,KAAK,EAAE,CAAC,CAAC;gBACZ,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,0GAA0G,CAAC,CAAC;SAC7H;IACH,CAAC;CAEF;AArJD,6BAqJC"}