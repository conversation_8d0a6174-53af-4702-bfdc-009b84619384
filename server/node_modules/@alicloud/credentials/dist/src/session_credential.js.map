{"version": 3, "file": "session_credential.js", "sourceRoot": "", "sources": ["../../src/session_credential.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,8EAAqD;AACrD,oDAAsC;AACtC,sDAA8B;AAC9B,0EAAiD;AAEjD,MAAqB,iBAAkB,SAAQ,4BAAiB;IAI9D,YAAY,MAAc;QACxB,MAAM,IAAI,GAAG,IAAI,gBAAM,CAAC;YACtB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,aAAa,EAAE,MAAM,CAAC,aAAa;SACpC,CAAC,CAAC;QACH,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,IAAI,IAAI,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC/C,IAAI,UAAU,EAAE;YACd,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC/B;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;IAC9C,CAAC;IAED,oBAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE;YAC5L,OAAO,IAAI,CAAC;SACb;QACD,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QACnF,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,GAAG,UAAU,EAAE;YAClD,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,0BAAe,CAAC;YACzB,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,WAAW;YAC/C,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC,eAAe;YACvD,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,aAAa;YACnD,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAC;IACL,CAAC;CACF;AA/DD,oCA+DC"}