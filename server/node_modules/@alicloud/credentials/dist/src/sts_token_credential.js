"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const default_credential_1 = __importDefault(require("./default_credential"));
const config_1 = __importDefault(require("./config"));
class StsTokenCredential extends default_credential_1.default {
    constructor(accessKeyId, accessKeySecret, securityToken) {
        if (!accessKeyId) {
            throw new Error('Missing required accessKeyId option in config for sts');
        }
        if (!accessKeySecret) {
            throw new Error('Missing required accessKeySecret option in config for sts');
        }
        if (!securityToken) {
            throw new Error('Missing required securityToken option in config for sts');
        }
        const conf = new config_1.default({
            type: 'sts',
            accessKeyId,
            accessKeySecret,
            securityToken
        });
        super(conf);
    }
}
exports.default = StsTokenCredential;
//# sourceMappingURL=sts_token_credential.js.map