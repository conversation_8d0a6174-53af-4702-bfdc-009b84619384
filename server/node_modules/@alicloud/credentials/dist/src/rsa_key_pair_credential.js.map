{"version": 3, "file": "rsa_key_pair_credential.js", "sourceRoot": "", "sources": ["../../src/rsa_key_pair_credential.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4CAAoB;AACpB,8EAAqD;AACrD,oDAAsC;AACtC,sCAAsC;AAEtC,sDAA8B;AAE9B,MAAM,iBAAiB,GAAG,mEAAmE,CAAC;AAE9F,MAAqB,oBAAqB,SAAQ,4BAAiB;IAKjE,YAAY,WAAmB,EAAE,cAAsB;QACrD,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;SACnF;QAED,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;SACtF;QAED,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,kBAAkB,cAAc,kBAAkB,CAAC,CAAC;SACrE;QAED,MAAM,IAAI,GAAG,IAAI,gBAAM,CAAC;YACtB,IAAI,EAAE,cAAc;SACrB,CAAC,CAAC;QACH,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,GAAG,GAAG,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9C,MAAM,IAAI,GAAG,MAAM,IAAA,cAAO,EAAC,GAAG,EAAE;YAC9B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,0BAA0B;YAClC,eAAe,EAAE,IAAI;YACrB,eAAe,EAAE,eAAe;YAChC,aAAa,EAAE,YAAY;SAC5B,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC;IAC5C,CAAC;CACF;AArCD,uCAqCC"}