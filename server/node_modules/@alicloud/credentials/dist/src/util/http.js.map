{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../../../src/util/http.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,kDAA0B;AAC1B,2CAA6B;AAC7B,iDAAmC;AACnC,+CAAiC;AAEjC,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AAE3D,SAAS,gBAAgB,CAAC,GAAW;IACnC,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,SAAS,YAAY,CAAC,MAA8B;IAClD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjC,MAAM,SAAS,GAA8B,EAAE,CAAC;IAChD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;KAChD;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,MAAM,CAAC,GAAW;IACzB,MAAM,MAAM,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAEvC,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;SAC/B,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;SACpB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;SACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;SACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,iBAAiB,CAAC,MAA8B,EAAE,GAAW,EAAE,MAAW;IACjF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEvB,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YACpC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,KAAK,MAAM,OAAO,IAAI,IAAI,EAAE;gBAC1B,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;aACtD;SACF;aAAM;YACL,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;SAClC;KACF;AACH,CAAC;AAED,SAAS,UAAU,CAAC,MAA8B;IAChD,MAAM,MAAM,GAA2B,EAAE,CAAC;IAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;SACvC;aAAM;YACL,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SACrB;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,SAAS,CAAC,MAA8B;IAC/C,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;IAClC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;IACxC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;KACpD;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,YAAY,CAAC,UAAsB;IAC1C,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,UAAU,EAAE;QACrC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;KAChC;IACD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,YAAY;IACnB,MAAM,aAAa,GAAG;QACpB,MAAM,EAAE,MAAM;QACd,eAAe,EAAE,WAAW;QAC5B,cAAc,EAAE,IAAI,CAAC,SAAS,EAAE;QAChC,gBAAgB,EAAE,KAAK;QACvB,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE;QAC5B,OAAO,EAAE,YAAY;QACrB,QAAQ,EAAE,aAAa;KAExB,CAAC;IACF,OAAO,aAAa,CAAC;AACvB,CAAC;AAEM,KAAK,UAAU,OAAO,CAAC,IAAY,EAAE,SAAiC,EAAE,EAAE,OAA+B,EAAE,EAAE,eAAwB;IAC1I,6BAA6B;IAC7B,IAAI,OAAO,mBACT,OAAO,EAAE;YACP,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,YAAY,EAAE,MAAM,CAAC,UAAU;SAChC,IACE,IAAI,CACR,CAAC;IAEF,4CAA4C;IAC5C,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,EAAE;QAClC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;KAC/B;IACD,MAAM,mCACD,YAAY,EAAE,GACd,MAAM,CACV,CAAC;IAEF,yBAAyB;IACzB,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;IACpD,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IACrC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QACtB,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;QAC/C,yBAAyB;QACzB,MAAM,YAAY,GAAG,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;QACzE,oBAAoB;QACpB,MAAM,GAAG,GAAG,eAAe,GAAG,GAAG,CAAC;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,QAAQ,CAAW,CAAC;QACnE,gBAAgB;QAChB,UAAU,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KACnD;IACD,wBAAwB;IACxB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;IACzF,kBAAkB;IAClB,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,mCAAmC,CAAC;QACnE,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;KACtC;IACD,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAChD,MAAM,MAAM,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAClD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAgB,CAAC,CAAC;IAC1C,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC5C,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAQ,CAAC;QAChD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;QAC/B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrB,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;QACd,MAAM,GAAG,CAAC;KACX;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AApDD,0BAoDC"}