{"name": "@alicloud/credentials", "version": "2.4.4", "description": "alibaba cloud node.js sdk credentials", "main": "dist/src/client.js", "scripts": {"prepublishOnly": "tsc", "build": "tsc", "lint": "eslint --fix ./src --ext .ts", "test": "mocha -b -r ts-node/register test/**/*.test.ts test/*.test.ts --timeout 15000", "test-dev": "nyc -e .ts -r=html -r=text -r=lcov mocha -b -r ts-node/register", "cov": "nyc -e .ts -r=html -r=text -r=lcov npm run test", "ci": "npm run cov", "integration": "mocha -b -r ts-node/register -R spec integration/*.test.ts", "clean": "rm -rf coverage"}, "repository": {"type": "git", "url": "git+https://github.com/aliyun/nodejs-credentials.git"}, "keywords": ["alibaba cloud", "sdk", "credentials"], "author": "Alibaba Cloud SDK", "license": "MIT", "devDependencies": {"@types/debug": "^4.1.12", "@types/expect.js": "^0.3.29", "@types/ini": "^1.3.30", "@types/mocha": "^10.0.6", "@types/node": "^20.11.10", "@types/rewire": "^2.5.28", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint": "^8.56.0", "expect.js": "^0.3.1", "mm": "^2.4.1", "mocha": "^10.1.0", "nyc": "^15.1.0", "rewire": "^7.0.0", "ts-node": "^10.9.2", "typescript": "^4.7.4"}, "dependencies": {"@alicloud/tea-typescript": "^1.8.0", "httpx": "^2.3.3", "ini": "^1.3.5", "kitx": "^2.0.0"}, "bugs": {"url": "https://github.com/aliyun/nodejs-credentials/issues"}, "homepage": "https://github.com/aliyun/nodejs-credentials#readme", "files": ["src", "dist"]}