{"name": "@alicloud/darabonba-signature-util", "version": "0.0.4", "description": "", "main": "dist/client.js", "scripts": {"test": "mocha -r ts-node/register ./tests/*test.ts", "test-cov": "nyc -e .ts -r=html -r=text -r=lcov npm run test", "build": "tsc", "prepublishOnly": "tsc"}, "author": "", "license": "ISC", "devDependencies": {"@types/mocha": "^9.0.0", "@types/node": "^12.20.37", "@types/should": "^13.0.0", "@types/sinon": "^10.0.6", "chai": "^4.3.4", "cross-env": "^7.0.3", "mocha": "^9.1.3", "nyc": "^15.1.0", "should": "^13.2.3", "sinon": "^12.0.1", "source-map-support": "^0.5.16", "ts-node": "^8.10.2", "tsconfig-paths": "^3.12.0", "typescript": "^3.7.5"}, "dependencies": {"@alicloud/darabonba-encode-util": "^0.0.1"}, "files": ["dist", "src"]}