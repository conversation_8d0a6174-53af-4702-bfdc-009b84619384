English | [简体中文](README-CN.md)
![](https://aliyunsdk-pages.alicdn.com/icons/AlibabaCloud.svg)

## Alibaba Cloud Darabonba String Util SDK for TypeScript/Node.js

## Prerequisite

Node.js >= 8.x

## Installation
If you use `npm` to manage your dependence, you can use the following command to install it and write into package.json dependences:

```sh
$ npm install @alicloud/darabonba-string -S
```

## Issues
[Opening an Issue](https://github.com/aliyun/darabonba-string/issues/new), Issues not conforming to the guidelines may be closed immediately.

## References
* [Latest Release](https://github.com/aliyun/darabonba-string/tree/master/ts)

## License
[Apache-2.0](http://www.apache.org/licenses/LICENSE-2.0)

Copyright (c) 2009-present, Alibaba Cloud All rights reserved.