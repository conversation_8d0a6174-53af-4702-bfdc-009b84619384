import * as $dara from '@darabonba/typescript';
export declare class QueryPageSmartShortUrlLogResponseBodyModelList extends $dara.Model {
    /**
     * @example
     * 87
     */
    clickState?: number;
    /**
     * @example
     * 51
     */
    clickTime?: number;
    /**
     * @example
     * 64
     */
    createTime?: number;
    /**
     * @example
     * 示例值示例值
     */
    phoneNumber?: string;
    /**
     * @example
     * 示例值示例值示例值
     */
    shortName?: string;
    /**
     * @example
     * 示例值示例值示例值
     */
    shortUrl?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
