import * as $dara from '@darabonba/typescript';
export declare class QueryCardSmsTemplateResponseBodyData extends $dara.Model {
    /**
     * @remarks
     * The array of objects.
     */
    templates?: {
        [key: string]: any;
    }[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
