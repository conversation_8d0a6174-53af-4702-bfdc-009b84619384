import * as $dara from '@darabonba/typescript';
export declare class RequiredPhoneCodeRequest extends $dara.Model {
    ownerId?: number;
    /**
     * @remarks
     * This parameter is required.
     *
     * @example
     * 137****1234
     */
    phoneNo?: string;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
