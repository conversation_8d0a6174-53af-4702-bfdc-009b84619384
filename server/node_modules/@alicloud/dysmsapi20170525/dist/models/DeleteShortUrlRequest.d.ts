import * as $dara from '@darabonba/typescript';
export declare class DeleteShortUrlRequest extends $dara.Model {
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * The source address. The address can be up to 1,000 characters in length.
     *
     * This parameter is required.
     *
     * @example
     * https://www.****.com/product/sms
     */
    sourceUrl?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
