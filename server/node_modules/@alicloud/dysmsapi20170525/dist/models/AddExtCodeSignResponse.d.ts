import * as $dara from '@darabonba/typescript';
import { AddExtCodeSignResponseBody } from "./AddExtCodeSignResponseBody";
export declare class AddExtCodeSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: AddExtCodeSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
