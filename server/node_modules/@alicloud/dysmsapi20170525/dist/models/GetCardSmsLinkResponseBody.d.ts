import * as $dara from '@darabonba/typescript';
import { GetCardSmsLinkResponseBodyData } from "./GetCardSmsLinkResponseBodyData";
export declare class GetCardSmsLinkResponseBody extends $dara.Model {
    /**
     * @remarks
     * The HTTP status code.
     *
     * *   The value OK indicates that the request was successful.
     * *   Other values indicate that the request failed. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The data returned.
     */
    data?: GetCardSmsLinkResponseBodyData;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * CC89A90C-978F-46AC-B80D-54738371E7CA
     */
    requestId?: string;
    /**
     * @remarks
     * Indicates whether the request is successful. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
