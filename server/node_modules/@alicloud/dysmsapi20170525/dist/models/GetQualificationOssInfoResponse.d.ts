import * as $dara from '@darabonba/typescript';
import { GetQualificationOssInfoResponseBody } from "./GetQualificationOssInfoResponseBody";
export declare class GetQualificationOssInfoResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: GetQualificationOssInfoResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
