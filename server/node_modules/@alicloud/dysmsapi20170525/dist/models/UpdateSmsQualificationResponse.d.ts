import * as $dara from '@darabonba/typescript';
import { UpdateSmsQualificationResponseBody } from "./UpdateSmsQualificationResponseBody";
export declare class UpdateSmsQualificationResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: UpdateSmsQualificationResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
