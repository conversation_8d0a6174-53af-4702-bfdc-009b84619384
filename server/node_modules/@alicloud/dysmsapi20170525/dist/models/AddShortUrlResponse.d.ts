import * as $dara from '@darabonba/typescript';
import { AddShortUrlResponseBody } from "./AddShortUrlResponseBody";
export declare class AddShortUrlResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: AddShortUrlResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
