import * as $dara from '@darabonba/typescript';
import { GetSmsSignResponseBody } from "./GetSmsSignResponseBody";
export declare class GetSmsSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: GetSmsSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
