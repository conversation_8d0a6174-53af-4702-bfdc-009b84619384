import * as $dara from '@darabonba/typescript';
/**
 */
export declare class AddShortUrlResponseBodyData extends $dara.Model {
    /**
     * @remarks
     * The time when the short URL expires.
     *
     * > The value of **ExpireDate** is on the hour.
     *
     * @example
     * 2021-09-19 00:00:00
     */
    expireDate?: string;
    /**
     * @remarks
     * The short URL.
     *
     * @example
     * http://****.cn/6y8uy7
     */
    shortUrl?: string;
    /**
     * @remarks
     * The source URL.
     *
     * @example
     * https://www.****.com/product/sms
     */
    sourceUrl?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
