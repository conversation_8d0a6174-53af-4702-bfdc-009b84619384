import * as $dara from '@darabonba/typescript';
export declare class QueryCardSmsTemplateReportResponseBodyData extends $dara.Model {
    /**
     * @remarks
     * The details of the data returned.
     */
    model?: {
        [key: string]: any;
    }[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
