import * as $dara from '@darabonba/typescript';
import { QuerySmsQualificationRecordResponseBody } from "./QuerySmsQualificationRecordResponseBody";
export declare class QuerySmsQualificationRecordResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QuerySmsQualificationRecordResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
