import * as $dara from '@darabonba/typescript';
import { QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO } from "./QuerySendDetailsResponseBodySmsSendDetailDtosSmsSendDetailDto";
export declare class QuerySendDetailsResponseBodySmsSendDetailDTOs extends $dara.Model {
    smsSendDetailDTO?: QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
