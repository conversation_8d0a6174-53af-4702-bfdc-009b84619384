import * as $dara from '@darabonba/typescript';
import { CreateCardSmsTemplateResponseBodyData } from "./CreateCardSmsTemplateResponseBodyData";
export declare class CreateCardSmsTemplateResponseBody extends $dara.Model {
    /**
     * @remarks
     * The response code.
     *
     * *   If OK is returned, the request is successful.
     * *   Other values indicate that the request fails. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The data returned.
     */
    data?: CreateCardSmsTemplateResponseBodyData;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * F655A8D5-B967-440B-8683-DAD6FF8DE990
     */
    requestId?: string;
    /**
     * @remarks
     * Indicates whether the request was successful. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
