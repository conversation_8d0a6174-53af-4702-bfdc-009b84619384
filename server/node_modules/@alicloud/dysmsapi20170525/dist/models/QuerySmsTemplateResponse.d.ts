import * as $dara from '@darabonba/typescript';
import { QuerySmsTemplateResponseBody } from "./QuerySmsTemplateResponseBody";
export declare class QuerySmsTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QuerySmsTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
