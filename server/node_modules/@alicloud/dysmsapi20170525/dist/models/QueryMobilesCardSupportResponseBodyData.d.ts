import * as $dara from '@darabonba/typescript';
import { QueryMobilesCardSupportResponseBodyDataQueryResult } from "./QueryMobilesCardSupportResponseBodyDataQueryResult";
export declare class QueryMobilesCardSupportResponseBodyData extends $dara.Model {
    /**
     * @remarks
     * The list of returned results.
     */
    queryResult?: QueryMobilesCardSupportResponseBodyDataQueryResult[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
