import * as $dara from '@darabonba/typescript';
export declare class CheckMobilesCardSupportResponseBodyDataQueryResult extends $dara.Model {
    /**
     * @remarks
     * The mobile phone number.
     *
     * @example
     * 1390000****
     */
    mobile?: string;
    /**
     * @remarks
     * Indicates whether the mobile phone number supports card messages.
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    support?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
