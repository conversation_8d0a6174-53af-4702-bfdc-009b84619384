import * as $dara from '@darabonba/typescript';
export declare class GetOSSInfoForUploadFileRequest extends $dara.Model {
    /**
     * @remarks
     * Business type, default value is **fcMediaSms**.
     *
     * When creating signatures and templates, and uploading **additional materials**, this value is **fcMediaSms**.
     *
     * @example
     * fcMediaSms
     */
    bizType?: string;
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
