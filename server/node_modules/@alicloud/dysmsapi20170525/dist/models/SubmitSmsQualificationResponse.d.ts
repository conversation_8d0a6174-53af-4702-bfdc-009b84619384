import * as $dara from '@darabonba/typescript';
import { SubmitSmsQualificationResponseBody } from "./SubmitSmsQualificationResponseBody";
export declare class SubmitSmsQualificationResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: SubmitSmsQualificationResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
