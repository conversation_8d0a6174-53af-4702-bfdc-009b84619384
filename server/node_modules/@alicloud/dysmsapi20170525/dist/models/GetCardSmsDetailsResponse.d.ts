import * as $dara from '@darabonba/typescript';
import { GetCardSmsDetailsResponseBody } from "./GetCardSmsDetailsResponseBody";
export declare class GetCardSmsDetailsResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: GetCardSmsDetailsResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
