import * as $dara from '@darabonba/typescript';
export declare class QueryShortUrlRequest extends $dara.Model {
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * The short URL. You can query the short URL by calling the [AddShortUrl](https://help.aliyun.com/document_detail/186774.html) operation.
     *
     * This parameter is required.
     *
     * @example
     * http://****.cn/6y8uy7
     */
    shortUrl?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
