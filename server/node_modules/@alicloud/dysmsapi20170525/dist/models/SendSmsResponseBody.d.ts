import * as $dara from '@darabonba/typescript';
export declare class SendSmsResponseBody extends $dara.Model {
    /**
     * @remarks
     * The ID of the delivery receipt.
     *
     * You can call the [QuerySendDetails](~~QuerySendDetails~~) operation to query the delivery status based on the receipt ID.
     *
     * @example
     * 9006197469364984****
     */
    bizId?: string;
    /**
     * @remarks
     * The HTTP status code.
     *
     * *   The value OK indicates that the request was successful.
     * *   Other values indicate that the request failed. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The returned message.
     *
     * @example
     * OK
     */
    message?: string;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * F655A8D5-B967-440B-8683-DAD6FF8DE990
     */
    requestId?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
