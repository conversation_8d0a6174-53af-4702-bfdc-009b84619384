import * as $dara from '@darabonba/typescript';
import { DeleteSmsSignResponseBody } from "./DeleteSmsSignResponseBody";
export declare class DeleteSmsSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: DeleteSmsSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
