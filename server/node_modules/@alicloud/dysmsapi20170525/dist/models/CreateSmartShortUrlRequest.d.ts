import * as $dara from '@darabonba/typescript';
export declare class CreateSmartShortUrlRequest extends $dara.Model {
    /**
     * @example
     * 示例值示例值
     */
    outId?: string;
    ownerId?: number;
    /**
     * @remarks
     * This parameter is required.
     *
     * @example
     * ***********
     */
    phoneNumbers?: string;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * This parameter is required.
     *
     * @example
     * 示例值
     */
    sourceUrl?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
