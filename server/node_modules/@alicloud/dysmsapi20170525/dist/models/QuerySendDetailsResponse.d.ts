import * as $dara from '@darabonba/typescript';
import { QuerySendDetailsResponseBody } from "./QuerySendDetailsResponseBody";
export declare class QuerySendDetailsResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QuerySendDetailsResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
