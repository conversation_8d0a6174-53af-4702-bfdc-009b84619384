import * as $dara from '@darabonba/typescript';
export declare class GetQualificationOssInfoRequest extends $dara.Model {
    /**
     * @remarks
     * 业务，非空
     *
     * This parameter is required.
     *
     * @example
     * dysms
     */
    bizType?: string;
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
