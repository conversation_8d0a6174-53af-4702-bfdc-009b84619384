import * as $dara from '@darabonba/typescript';
import { ValidPhoneCodeResponseBody } from "./ValidPhoneCodeResponseBody";
export declare class ValidPhoneCodeResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: ValidPhoneCodeResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
