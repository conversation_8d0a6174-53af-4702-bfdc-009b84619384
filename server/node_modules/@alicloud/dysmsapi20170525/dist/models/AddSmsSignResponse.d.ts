import * as $dara from '@darabonba/typescript';
import { AddSmsSignResponseBody } from "./AddSmsSignResponseBody";
export declare class AddSmsSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: AddSmsSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
