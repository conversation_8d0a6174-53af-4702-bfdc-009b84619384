import * as $dara from '@darabonba/typescript';
import { SendBatchCardSmsResponseBody } from "./SendBatchCardSmsResponseBody";
export declare class SendBatchCardSmsResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: SendBatchCardSmsResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
