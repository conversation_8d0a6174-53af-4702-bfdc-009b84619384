"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuerySingleSmsQualificationResponseBodyData = void 0;
// This file is auto-generated, don't edit it
const $dara = __importStar(require("@darabonba/typescript"));
const QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics_1 = require("./QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics");
const QuerySingleSmsQualificationResponseBodyDataOtherFiles_1 = require("./QuerySingleSmsQualificationResponseBodyDataOtherFiles");
class QuerySingleSmsQualificationResponseBodyData extends $dara.Model {
    static names() {
        return {
            adminIDCardExpDate: 'AdminIDCardExpDate',
            adminIDCardFrontFace: 'AdminIDCardFrontFace',
            adminIDCardNo: 'AdminIDCardNo',
            adminIDCardPic: 'AdminIDCardPic',
            adminIDCardType: 'AdminIDCardType',
            adminName: 'AdminName',
            adminPhoneNo: 'AdminPhoneNo',
            businessLicensePics: 'BusinessLicensePics',
            businessType: 'BusinessType',
            companyName: 'CompanyName',
            companyType: 'CompanyType',
            effTimeStr: 'EffTimeStr',
            legalPersonIDCardNo: 'LegalPersonIDCardNo',
            legalPersonIDCardType: 'LegalPersonIDCardType',
            legalPersonIdCardEffTime: 'LegalPersonIdCardEffTime',
            legalPersonName: 'LegalPersonName',
            organizationCode: 'OrganizationCode',
            otherFiles: 'OtherFiles',
            qualificationGroupId: 'QualificationGroupId',
            qualificationName: 'QualificationName',
            remark: 'Remark',
            state: 'State',
            useBySelf: 'UseBySelf',
            whetherShare: 'WhetherShare',
            workOrderId: 'WorkOrderId',
        };
    }
    static types() {
        return {
            adminIDCardExpDate: 'string',
            adminIDCardFrontFace: 'string',
            adminIDCardNo: 'string',
            adminIDCardPic: 'string',
            adminIDCardType: 'string',
            adminName: 'string',
            adminPhoneNo: 'string',
            businessLicensePics: { 'type': 'array', 'itemType': QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics_1.QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics },
            businessType: 'string',
            companyName: 'string',
            companyType: 'string',
            effTimeStr: 'string',
            legalPersonIDCardNo: 'string',
            legalPersonIDCardType: 'string',
            legalPersonIdCardEffTime: 'string',
            legalPersonName: 'string',
            organizationCode: 'string',
            otherFiles: { 'type': 'array', 'itemType': QuerySingleSmsQualificationResponseBodyDataOtherFiles_1.QuerySingleSmsQualificationResponseBodyDataOtherFiles },
            qualificationGroupId: 'number',
            qualificationName: 'string',
            remark: 'string',
            state: 'string',
            useBySelf: 'boolean',
            whetherShare: 'boolean',
            workOrderId: 'number',
        };
    }
    validate() {
        if (Array.isArray(this.businessLicensePics)) {
            $dara.Model.validateArray(this.businessLicensePics);
        }
        if (Array.isArray(this.otherFiles)) {
            $dara.Model.validateArray(this.otherFiles);
        }
        super.validate();
    }
    constructor(map) {
        super(map);
    }
}
exports.QuerySingleSmsQualificationResponseBodyData = QuerySingleSmsQualificationResponseBodyData;
//# sourceMappingURL=QuerySingleSmsQualificationResponseBodyData.js.map