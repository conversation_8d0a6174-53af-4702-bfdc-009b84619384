import * as $dara from '@darabonba/typescript';
export declare class TagResourcesResponseBody extends $dara.Model {
    /**
     * @remarks
     * The response code.
     *
     * *   If OK is returned, the request is successful.
     * *   Other values indicate that the request fails. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * Indicates whether tags were attached. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    data?: string;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * A90E4451-FED7-49D2-87C8-00700A8C****
     */
    requestId?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
