import * as $dara from '@darabonba/typescript';
import { TagResourcesRequestTag } from "./TagResourcesRequestTag";
export declare class TagResourcesRequest extends $dara.Model {
    ownerId?: number;
    /**
     * @remarks
     * The name of the cloud service. Set the value to **dysms**.
     *
     * @example
     * dysms
     */
    prodCode?: string;
    /**
     * @remarks
     * The region ID. Set the value to **cn-hangzhou**.
     *
     * This parameter is required.
     *
     * @example
     * cn-hangzhou
     */
    regionId?: string;
    /**
     * @remarks
     * The code of the message template.
     *
     * @example
     * SMS_23423423
     */
    resourceId?: string[];
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * The type of the resource. Set the value to **TEMPLATE**.
     *
     * This parameter is required.
     *
     * @example
     * TEMPLATE
     */
    resourceType?: string;
    /**
     * @remarks
     * The tag.
     *
     * This parameter is required.
     */
    tag?: TagResourcesRequestTag[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
