import * as $dara from '@darabonba/typescript';
export declare class ListTagResourcesRequestTag extends $dara.Model {
    /**
     * @remarks
     * The key of the tag.
     *
     * @example
     * TestKey
     */
    key?: string;
    /**
     * @remarks
     * The value of the tag.
     *
     * @example
     * TestValue
     */
    value?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
