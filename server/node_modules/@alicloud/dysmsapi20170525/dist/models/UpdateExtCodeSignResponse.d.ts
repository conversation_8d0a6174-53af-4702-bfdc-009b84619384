import * as $dara from '@darabonba/typescript';
import { UpdateExtCodeSignResponseBody } from "./UpdateExtCodeSignResponseBody";
export declare class UpdateExtCodeSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: UpdateExtCodeSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
