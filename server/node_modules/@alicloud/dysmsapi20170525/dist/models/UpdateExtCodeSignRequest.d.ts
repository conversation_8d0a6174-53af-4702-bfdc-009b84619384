import * as $dara from '@darabonba/typescript';
export declare class UpdateExtCodeSignRequest extends $dara.Model {
    /**
     * @remarks
     * 要修改的扩展码A3
     *
     * This parameter is required.
     *
     * @example
     * 01
     */
    existExtCode?: string;
    /**
     * @remarks
     * 修改后的扩展码A3
     *
     * This parameter is required.
     *
     * @example
     * 02
     */
    newExtCode?: string;
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * 签名
     *
     * This parameter is required.
     *
     * @example
     * 示例值示例值示例值
     */
    signName?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
