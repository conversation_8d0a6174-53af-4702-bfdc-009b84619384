import * as $dara from '@darabonba/typescript';
export declare class TagResourcesRequestTag extends $dara.Model {
    /**
     * @remarks
     * The array of tag keys. Valid values of N: 1 to 20.
     *
     * @example
     * TestKey
     */
    key?: string;
    /**
     * @remarks
     * The array of tag values. Valid values of N: 1 to 20.
     *
     * @example
     * TestValue
     */
    value?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
