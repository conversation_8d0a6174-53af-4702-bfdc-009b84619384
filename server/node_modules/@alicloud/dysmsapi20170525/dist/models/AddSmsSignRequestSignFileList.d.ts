import * as $dara from '@darabonba/typescript';
export declare class AddSmsSignRequestSignFileList extends $dara.Model {
    /**
     * @remarks
     * The Base64-encoded string of the qualification document. An image cannot exceed 2 MB in size. In some scenarios, you must upload supporting documents to apply for signatures. For more information, see [SMS signature specifications](https://help.aliyun.com/document_detail/108076.html).
     *
     * This parameter is required.
     *
     * @example
     * R0lGODlhHAAmAKIHAKqqqsvLy0hISObm5vf394uL****
     */
    fileContents?: string;
    /**
     * @remarks
     * The format of the qualification document. You can upload multiple images. Images in JPG, PNG, GIF, or JPEG format are supported.
     *
     * In some scenarios, you must upload supporting documents to apply for signatures. For more information, see [SMS signature specifications](https://help.aliyun.com/document_detail/108076.html).
     *
     * > If you apply for a signature for other users or if the signature source is the name of an enterprise or public institution, you must upload a certificate and a letter of authorization. For more information, see [Certificate](https://help.aliyun.com/document_detail/108076.html) and [Letter of authorization](https://help.aliyun.com/document_detail/56741.html).
     *
     * This parameter is required.
     *
     * @example
     * jpg
     */
    fileSuffix?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
