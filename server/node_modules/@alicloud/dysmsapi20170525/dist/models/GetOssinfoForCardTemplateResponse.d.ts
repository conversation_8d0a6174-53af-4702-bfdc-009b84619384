import * as $dara from '@darabonba/typescript';
import { GetOSSInfoForCardTemplateResponseBody } from "./GetOssinfoForCardTemplateResponseBody";
export declare class GetOSSInfoForCardTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: GetOSSInfoForCardTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
