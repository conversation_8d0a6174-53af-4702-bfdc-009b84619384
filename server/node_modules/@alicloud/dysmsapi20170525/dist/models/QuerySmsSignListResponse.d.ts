import * as $dara from '@darabonba/typescript';
import { QuerySmsSignListResponseBody } from "./QuerySmsSignListResponseBody";
export declare class QuerySmsSignListResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QuerySmsSignListResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
