import * as $dara from '@darabonba/typescript';
import { SmsConversionIntlResponseBody } from "./SmsConversionIntlResponseBody";
export declare class SmsConversionIntlResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: SmsConversionIntlResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
