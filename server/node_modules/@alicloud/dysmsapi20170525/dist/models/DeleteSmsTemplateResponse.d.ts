import * as $dara from '@darabonba/typescript';
import { DeleteSmsTemplateResponseBody } from "./DeleteSmsTemplateResponseBody";
export declare class DeleteSmsTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: DeleteSmsTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
