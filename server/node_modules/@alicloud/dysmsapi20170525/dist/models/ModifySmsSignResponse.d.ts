import * as $dara from '@darabonba/typescript';
import { ModifySmsSignResponseBody } from "./ModifySmsSignResponseBody";
export declare class ModifySmsSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: ModifySmsSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
