import * as $dara from '@darabonba/typescript';
import { GetOSSInfoForUploadFileResponseBodyModel } from "./GetOssinfoForUploadFileResponseBodyModel";
export declare class GetOSSInfoForUploadFileResponseBody extends $dara.Model {
    /**
     * @remarks
     * Request status code.
     *
     * - OK return represents a successful request.
     * - For other error codes, please refer to the [Error Code List](https://help.aliyun.com/document_detail/101346.htm).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * Description of the status code.
     *
     * @example
     * OK
     */
    message?: string;
    /**
     * @remarks
     * Return result.
     */
    model?: GetOSSInfoForUploadFileResponseBodyModel;
    /**
     * @remarks
     * The ID of this call request, which is a unique identifier generated by Alibaba Cloud for the request, can be used for troubleshooting and issue定位.
     *
     * @example
     * A90E4451-FED7-49D2-87C8-00700EDCFD0D
     */
    requestId?: string;
    /**
     * @remarks
     * Indicates success. Values:
     *
     * - **true**
     * - **false**
     *
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
