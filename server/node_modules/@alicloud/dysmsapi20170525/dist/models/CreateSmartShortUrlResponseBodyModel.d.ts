import * as $dara from '@darabonba/typescript';
export declare class CreateSmartShortUrlResponseBodyModel extends $dara.Model {
    /**
     * @example
     * 示例值
     */
    domain?: string;
    /**
     * @example
     * 11
     */
    expiration?: number;
    /**
     * @example
     * 示例值
     */
    phoneNumber?: string;
    /**
     * @example
     * 示例值
     */
    shortName?: string;
    /**
     * @example
     * 示例值示例值
     */
    shortUrl?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
