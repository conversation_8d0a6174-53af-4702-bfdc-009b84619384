import * as $dara from '@darabonba/typescript';
import { SendCardSmsResponseBody } from "./SendCardSmsResponseBody";
export declare class SendCardSmsResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: SendCardSmsResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
