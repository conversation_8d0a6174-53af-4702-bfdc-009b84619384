import * as $dara from '@darabonba/typescript';
import { QueryPageSmartShortUrlLogResponseBodyModel } from "./QueryPageSmartShortUrlLogResponseBodyModel";
export declare class QueryPageSmartShortUrlLogResponseBody extends $dara.Model {
    /**
     * @example
     * 示例值示例值
     */
    code?: string;
    /**
     * @example
     * 示例值示例值
     */
    message?: string;
    model?: QueryPageSmartShortUrlLogResponseBodyModel;
    /**
     * @example
     * 示例值示例值
     */
    requestId?: string;
    /**
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
