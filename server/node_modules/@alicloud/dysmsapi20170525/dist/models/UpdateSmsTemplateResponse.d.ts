import * as $dara from '@darabonba/typescript';
import { UpdateSmsTemplateResponseBody } from "./UpdateSmsTemplateResponseBody";
export declare class UpdateSmsTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: UpdateSmsTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
