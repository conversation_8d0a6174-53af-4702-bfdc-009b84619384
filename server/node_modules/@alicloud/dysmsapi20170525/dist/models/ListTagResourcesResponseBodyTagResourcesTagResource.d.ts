import * as $dara from '@darabonba/typescript';
export declare class ListTagResourcesResponseBodyTagResourcesTagResource extends $dara.Model {
    /**
     * @remarks
     * The code of the message template.
     *
     * @example
     * SMS_23423****
     */
    resourceId?: string;
    /**
     * @remarks
     * The type of resource.
     *
     * @example
     * ALIYUN::DYSMS::TEMPLATE
     */
    resourceType?: string;
    /**
     * @remarks
     * The tag key.
     *
     * @example
     * TestKey
     */
    tagKey?: string;
    /**
     * @remarks
     * The tag value.
     *
     * @example
     * TestValue
     */
    tagValue?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
