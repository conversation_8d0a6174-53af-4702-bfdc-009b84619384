import * as $dara from '@darabonba/typescript';
import { QuerySmsAuthorizationLetterResponseBody } from "./QuerySmsAuthorizationLetterResponseBody";
export declare class QuerySmsAuthorizationLetterResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QuerySmsAuthorizationLetterResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
