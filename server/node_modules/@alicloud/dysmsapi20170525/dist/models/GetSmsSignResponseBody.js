"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetSmsSignResponseBody = void 0;
// This file is auto-generated, don't edit it
const $dara = __importStar(require("@darabonba/typescript"));
const GetSmsSignResponseBodyAuditInfo_1 = require("./GetSmsSignResponseBodyAuditInfo");
const GetSmsSignResponseBodySignIspRegisterDetailList_1 = require("./GetSmsSignResponseBodySignIspRegisterDetailList");
class GetSmsSignResponseBody extends $dara.Model {
    static names() {
        return {
            applyScene: 'ApplyScene',
            auditInfo: 'AuditInfo',
            authorizationLetterAuditPass: 'AuthorizationLetterAuditPass',
            authorizationLetterId: 'AuthorizationLetterId',
            code: 'Code',
            createDate: 'CreateDate',
            fileUrlList: 'FileUrlList',
            message: 'Message',
            orderId: 'OrderId',
            qualificationId: 'QualificationId',
            registerResult: 'RegisterResult',
            remark: 'Remark',
            requestId: 'RequestId',
            signCode: 'SignCode',
            signIspRegisterDetailList: 'SignIspRegisterDetailList',
            signName: 'SignName',
            signStatus: 'SignStatus',
            signTag: 'SignTag',
            signUsage: 'SignUsage',
            thirdParty: 'ThirdParty',
        };
    }
    static types() {
        return {
            applyScene: 'string',
            auditInfo: GetSmsSignResponseBodyAuditInfo_1.GetSmsSignResponseBodyAuditInfo,
            authorizationLetterAuditPass: 'boolean',
            authorizationLetterId: 'number',
            code: 'string',
            createDate: 'string',
            fileUrlList: { 'type': 'array', 'itemType': 'string' },
            message: 'string',
            orderId: 'string',
            qualificationId: 'number',
            registerResult: 'number',
            remark: 'string',
            requestId: 'string',
            signCode: 'string',
            signIspRegisterDetailList: { 'type': 'array', 'itemType': GetSmsSignResponseBodySignIspRegisterDetailList_1.GetSmsSignResponseBodySignIspRegisterDetailList },
            signName: 'string',
            signStatus: 'number',
            signTag: 'string',
            signUsage: 'string',
            thirdParty: 'boolean',
        };
    }
    validate() {
        if (this.auditInfo && typeof this.auditInfo.validate === 'function') {
            this.auditInfo.validate();
        }
        if (Array.isArray(this.fileUrlList)) {
            $dara.Model.validateArray(this.fileUrlList);
        }
        if (Array.isArray(this.signIspRegisterDetailList)) {
            $dara.Model.validateArray(this.signIspRegisterDetailList);
        }
        super.validate();
    }
    constructor(map) {
        super(map);
    }
}
exports.GetSmsSignResponseBody = GetSmsSignResponseBody;
//# sourceMappingURL=GetSmsSignResponseBody.js.map