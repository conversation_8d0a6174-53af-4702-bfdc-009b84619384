import * as $dara from '@darabonba/typescript';
export declare class DeleteSmsTemplateResponseBody extends $dara.Model {
    /**
     * @remarks
     * The response code.
     *
     * *   The value OK indicates that the request was successful.
     * *   For more information about other response codes, see [API error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The returned message.
     *
     * @example
     * OK
     */
    message?: string;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * CCA2BCFF-2BA7-427C-90EE-AC6994748607
     */
    requestId?: string;
    /**
     * @remarks
     * The code of the message template.
     *
     * @example
     * SMS_20375****
     */
    templateCode?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
