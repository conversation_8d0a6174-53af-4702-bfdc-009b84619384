import * as $dara from '@darabonba/typescript';
import { QueryPageSmartShortUrlLogResponseBody } from "./QueryPageSmartShortUrlLogResponseBody";
export declare class QueryPageSmartShortUrlLogResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QueryPageSmartShortUrlLogResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
