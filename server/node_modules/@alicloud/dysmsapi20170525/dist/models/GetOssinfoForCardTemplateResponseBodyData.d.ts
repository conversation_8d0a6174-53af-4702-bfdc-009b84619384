import * as $dara from '@darabonba/typescript';
export declare class GetOSSInfoForCardTemplateResponseBodyData extends $dara.Model {
    /**
     * @remarks
     * The AccessKey ID.
     *
     * @example
     * LTAIxetqt1Dg****
     */
    accessKeyId?: string;
    /**
     * @remarks
     * The ID of the Alibaba Cloud account.
     *
     * @example
     * ****************
     */
    aliUid?: string;
    /**
     * @remarks
     * The name of the OSS bucket.
     *
     * @example
     * alicom-cardsms-resources
     */
    bucket?: string;
    /**
     * @remarks
     * The timeout period.
     *
     * @example
     * **********
     */
    expireTime?: string;
    /**
     * @remarks
     * The hostname.
     *
     * @example
     * https://alicom-cardsms-resources.oss-cn-zhangjiakou.aliyuncs.com
     */
    host?: string;
    /**
     * @remarks
     * The signature policy.
     *
     * @example
     * eyJxxx0=
     */
    policy?: string;
    /**
     * @remarks
     * The signature.
     *
     * @example
     * Aliyun
     */
    signature?: string;
    /**
     * @remarks
     * The path of the policy.
     *
     * @example
     * **********
     */
    startPath?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
