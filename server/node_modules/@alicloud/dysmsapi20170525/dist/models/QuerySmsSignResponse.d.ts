import * as $dara from '@darabonba/typescript';
import { QuerySmsSignResponseBody } from "./QuerySmsSignResponseBody";
export declare class QuerySmsSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QuerySmsSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
