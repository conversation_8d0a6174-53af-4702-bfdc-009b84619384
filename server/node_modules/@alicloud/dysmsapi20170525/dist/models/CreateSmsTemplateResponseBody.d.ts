import * as $dara from '@darabonba/typescript';
export declare class CreateSmsTemplateResponseBody extends $dara.Model {
    /**
     * @remarks
     * Request status code.
     *
     * * OK indicates a successful request.
     * * For other error codes, refer to the **Error Codes** section of this chapter or the product\\"s [API Error Codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * Description of the status code.
     *
     * @example
     * successful
     */
    message?: string;
    /**
     * @remarks
     * Work order ID.
     *
     * This parameter is used by auditors when querying audits. If you need expedited review, you must provide this work order number.
     *
     * @example
     * 2005020****
     */
    orderId?: string;
    /**
     * @remarks
     * The ID generated by Alibaba Cloud for this request, which is a unique identifier that can be used for troubleshooting and issue定位.
     *
     * @example
     * F655A8D5-B967-440B-8683-DAD6FF8DE990
     */
    requestId?: string;
    /**
     * @remarks
     * SMS template code.
     *
     * After submitting the template application, you can use the SMS template code to query the template audit details via the [GetSmsTemplate](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-getsmstemplate?) API. You can also [configure delivery receipts](https://help.aliyun.com/zh/sms/developer-reference/configure-delivery-receipts-1?spm), and obtain the template audit status messages through TemplateSmsReport.
     *
     * @example
     * SMS_10000****
     */
    templateCode?: string;
    /**
     * @remarks
     * SMS template name.
     *
     * @example
     * aliyunCode
     */
    templateName?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
