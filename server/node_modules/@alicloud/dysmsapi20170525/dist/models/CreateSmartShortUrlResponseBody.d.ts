import * as $dara from '@darabonba/typescript';
import { CreateSmartShortUrlResponseBodyModel } from "./CreateSmartShortUrlResponseBodyModel";
export declare class CreateSmartShortUrlResponseBody extends $dara.Model {
    /**
     * @example
     * 示例值示例值示例值
     */
    code?: string;
    /**
     * @example
     * 示例值示例值示例值
     */
    message?: string;
    model?: CreateSmartShortUrlResponseBodyModel[];
    /**
     * @example
     * 示例值示例值
     */
    requestId?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
