import * as $dara from '@darabonba/typescript';
import { ChangeSignatureQualificationResponseBodyData } from "./ChangeSignatureQualificationResponseBodyData";
export declare class ChangeSignatureQualificationResponseBody extends $dara.Model {
    accessDeniedDetail?: string;
    /**
     * @example
     * OK
     */
    code?: string;
    data?: ChangeSignatureQualificationResponseBodyData;
    /**
     * @example
     * OK
     */
    message?: string;
    /**
     * @example
     * 0A974B78-02BF-4C79-ADF3-90CFBA1B55B1
     */
    requestId?: string;
    /**
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
