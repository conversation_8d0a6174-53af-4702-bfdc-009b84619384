import * as $dara from '@darabonba/typescript';
export declare class GetSmsSignResponseBodyAuditInfo extends $dara.Model {
    /**
     * @remarks
     * Audit date and time.
     *
     * @example
     * 2024-06-03 12:02:34
     */
    auditDate?: string;
    /**
     * @remarks
     * Reasons for not passing the review.
     *
     * @example
     * reason for rejection.
     */
    rejectInfo?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
