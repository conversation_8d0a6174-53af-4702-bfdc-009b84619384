import * as $dara from '@darabonba/typescript';
export declare class QuerySendStatisticsResponseBodyDataTargetList extends $dara.Model {
    /**
     * @remarks
     * The number of messages without a delivery receipt.
     *
     * @example
     * 1
     */
    noRespondedCount?: number;
    /**
     * @remarks
     * The number of messages with a delivery receipt that indicates a failure.
     *
     * @example
     * 2
     */
    respondedFailCount?: number;
    /**
     * @remarks
     * The number of messages with a delivery receipt that indicates a success.
     *
     * @example
     * 17
     */
    respondedSuccessCount?: number;
    /**
     * @remarks
     * The date when the message is sent. Format: yyyyMMdd. Example: 20181225.
     *
     * @example
     * 20201010
     */
    sendDate?: string;
    /**
     * @remarks
     * The number of delivered messages.
     *
     * @example
     * 20
     */
    totalCount?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
