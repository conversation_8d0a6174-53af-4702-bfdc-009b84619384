import * as $dara from '@darabonba/typescript';
import { GetOSSInfoForCardTemplateResponseBodyData } from "./GetOssinfoForCardTemplateResponseBodyData";
export declare class GetOSSInfoForCardTemplateResponseBody extends $dara.Model {
    /**
     * @remarks
     * The HTTP status code.
     *
     * *   The value OK indicates that the request was successful.
     * *   For more information about other response codes, see [API error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The data returned.
     */
    data?: GetOSSInfoForCardTemplateResponseBodyData;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * A90E4451-FED7-49D2-87C8-00700A8C4D0D
     */
    requestId?: string;
    /**
     * @remarks
     * Indicates whether the request is successful. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
