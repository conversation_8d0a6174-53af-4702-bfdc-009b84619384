import * as $dara from '@darabonba/typescript';
import { CheckMobilesCardSupportResponseBodyData } from "./CheckMobilesCardSupportResponseBodyData";
export declare class CheckMobilesCardSupportResponseBody extends $dara.Model {
    /**
     * @remarks
     * The HTTP status code.
     *
     * *   The value OK indicates that the request was successful.
     * *   Other values indicate that the request failed. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The data returned.
     */
    data?: CheckMobilesCardSupportResponseBodyData;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * 819BE656-D2E0-4858-8B21-B2E477085AAF
     */
    requestId?: string;
    /**
     * @remarks
     * Indicates whether the request is successful. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
