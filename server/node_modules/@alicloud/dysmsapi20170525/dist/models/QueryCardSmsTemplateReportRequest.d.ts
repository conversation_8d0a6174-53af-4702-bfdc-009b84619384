import * as $dara from '@darabonba/typescript';
export declare class QueryCardSmsTemplateReportRequest extends $dara.Model {
    /**
     * @remarks
     * The end of the time range to query. Specify the time in the yyyy-MM-dd HH:mm:ss format.
     *
     * @example
     * 2020-10-11 00:00:01
     */
    endDate?: string;
    /**
     * @remarks
     * The beginning of the time range to query. Specify the time in the yyyy-MM-dd HH:mm:ss format.
     *
     * @example
     * 2020-10-10 00:00:01
     */
    startDate?: string;
    /**
     * @remarks
     * The array of message templates.
     *
     * This parameter is required.
     */
    templateCodes?: string[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
