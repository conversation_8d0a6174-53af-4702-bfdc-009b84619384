import * as $dara from '@darabonba/typescript';
import { RequiredPhoneCodeResponseBody } from "./RequiredPhoneCodeResponseBody";
export declare class RequiredPhoneCodeResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: RequiredPhoneCodeResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
