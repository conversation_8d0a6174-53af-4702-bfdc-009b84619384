import * as $dara from '@darabonba/typescript';
export declare class QuerySmsTemplateListRequest extends $dara.Model {
    ownerId?: number;
    /**
     * @remarks
     * The page number. Default value: **1**.
     *
     * @example
     * 1
     */
    pageIndex?: number;
    /**
     * @remarks
     * The number of templates per page. Valid values: **1 to 50**.
     *
     * @example
     * 10
     */
    pageSize?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
