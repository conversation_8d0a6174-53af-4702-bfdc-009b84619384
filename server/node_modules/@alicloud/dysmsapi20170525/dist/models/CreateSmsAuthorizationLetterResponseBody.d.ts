import * as $dara from '@darabonba/typescript';
export declare class CreateSmsAuthorizationLetterResponseBody extends $dara.Model {
    accessDeniedDetail?: string;
    /**
     * @example
     * OK
     */
    code?: string;
    /**
     * @example
     * 10000****
     */
    data?: string;
    /**
     * @example
     * OK
     */
    message?: string;
    /**
     * @example
     * F655A8D5-B967-440B-8683-DAD6FF8DE990
     */
    requestId?: string;
    /**
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
