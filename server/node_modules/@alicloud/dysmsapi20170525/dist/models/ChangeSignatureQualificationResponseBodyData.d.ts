import * as $dara from '@darabonba/typescript';
export declare class ChangeSignatureQualificationResponseBodyData extends $dara.Model {
    data?: {
        [key: string]: any;
    };
    /**
     * @example
     * 示例值
     */
    errCode?: string;
    /**
     * @example
     * 示例值示例值
     */
    errMessage?: string;
    /**
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
