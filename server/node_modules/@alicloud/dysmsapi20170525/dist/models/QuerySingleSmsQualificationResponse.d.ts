import * as $dara from '@darabonba/typescript';
import { QuerySingleSmsQualificationResponseBody } from "./QuerySingleSmsQualificationResponseBody";
export declare class QuerySingleSmsQualificationResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QuerySingleSmsQualificationResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
