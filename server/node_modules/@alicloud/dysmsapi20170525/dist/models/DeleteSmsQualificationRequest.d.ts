import * as $dara from '@darabonba/typescript';
export declare class DeleteSmsQualificationRequest extends $dara.Model {
    /**
     * @remarks
     * 工单ID
     *
     * This parameter is required.
     *
     * @example
     * 2001****
     */
    orderId?: number;
    ownerId?: number;
    /**
     * @remarks
     * 资质组ID
     *
     * This parameter is required.
     *
     * @example
     * 10000****
     */
    qualificationGroupId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
