import * as $dara from '@darabonba/typescript';
import { GetSmsTemplateResponseBody } from "./GetSmsTemplateResponseBody";
export declare class GetSmsTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: GetSmsTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
