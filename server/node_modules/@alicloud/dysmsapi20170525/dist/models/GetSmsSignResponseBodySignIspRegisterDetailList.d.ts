import * as $dara from '@darabonba/typescript';
import { GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons } from "./GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons";
export declare class GetSmsSignResponseBodySignIspRegisterDetailList extends $dara.Model {
    operatorCode?: string;
    operatorCompleteTime?: string;
    registerStatus?: number;
    registerStatusReasons?: GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
