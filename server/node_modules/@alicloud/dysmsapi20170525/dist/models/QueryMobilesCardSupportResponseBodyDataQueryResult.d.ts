import * as $dara from '@darabonba/typescript';
export declare class QueryMobilesCardSupportResponseBodyDataQueryResult extends $dara.Model {
    /**
     * @remarks
     * The mobile phone number.
     *
     * @example
     * 1380000****
     */
    mobile?: string;
    /**
     * @remarks
     * Indicates whether the mobile phone number supports card messages. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    support?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
