import * as $dara from '@darabonba/typescript';
import { ConversionDataIntlResponseBody } from "./ConversionDataIntlResponseBody";
export declare class ConversionDataIntlResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: ConversionDataIntlResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
