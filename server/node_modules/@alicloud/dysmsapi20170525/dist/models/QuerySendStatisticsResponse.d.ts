import * as $dara from '@darabonba/typescript';
import { QuerySendStatisticsResponseBody } from "./QuerySendStatisticsResponseBody";
export declare class QuerySendStatisticsResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QuerySendStatisticsResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
