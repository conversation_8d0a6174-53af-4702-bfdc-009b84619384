import * as $dara from '@darabonba/typescript';
export declare class SmsConversionIntlRequest extends $dara.Model {
    /**
     * @remarks
     * The time when the OTP message was delivered. The value is a UNIX timestamp. Unit: milliseconds.
     *
     * *   If you leave the parameter empty, the current timestamp is specified by default.
     * *   If you specify the parameter, the timestamp must be greater than the message sending time and less than the current timestamp.
     *
     * @example
     * *************
     */
    conversionTime?: number;
    /**
     * @remarks
     * Specifies whether customers replied to the OTP message. Valid values: true and false.
     *
     * This parameter is required.
     *
     * @example
     * true
     */
    delivered?: boolean;
    /**
     * @remarks
     * The ID of the message.
     *
     * This parameter is required.
     *
     * @example
     * **************
     */
    messageId?: string;
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
