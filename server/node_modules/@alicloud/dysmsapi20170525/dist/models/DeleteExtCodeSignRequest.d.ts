import * as $dara from '@darabonba/typescript';
export declare class DeleteExtCodeSignRequest extends $dara.Model {
    /**
     * @remarks
     * 扩展码A3
     *
     * This parameter is required.
     *
     * @example
     * 01
     */
    extCode?: string;
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * 签名
     *
     * This parameter is required.
     *
     * @example
     * 示例值
     */
    signName?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
