import * as $dara from '@darabonba/typescript';
import { GetOSSInfoForUploadFileResponseBody } from "./GetOssinfoForUploadFileResponseBody";
export declare class GetOSSInfoForUploadFileResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: GetOSSInfoForUploadFileResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
