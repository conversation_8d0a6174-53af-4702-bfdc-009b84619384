import * as $dara from '@darabonba/typescript';
export declare class SubmitSmsQualificationResponseBody extends $dara.Model {
    accessDeniedDetail?: string;
    /**
     * @example
     * OK
     */
    code?: string;
    /**
     * @example
     * 1000****
     */
    data?: string;
    /**
     * @example
     * OK
     */
    message?: string;
    /**
     * @example
     * 25D5AFDE-8EBC-132E-8909-1FDC071DA
     */
    requestId?: string;
    /**
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
