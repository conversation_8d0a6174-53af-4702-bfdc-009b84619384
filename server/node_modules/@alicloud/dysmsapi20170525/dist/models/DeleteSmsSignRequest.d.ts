import * as $dara from '@darabonba/typescript';
export declare class DeleteSmsSignRequest extends $dara.Model {
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * The signature.
     *
     * > The signature must be submitted by the current Alibaba Cloud account, and has been approved.
     *
     * This parameter is required.
     *
     * @example
     * Aliyun
     */
    signName?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
