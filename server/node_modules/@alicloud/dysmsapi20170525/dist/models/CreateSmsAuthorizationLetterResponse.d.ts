import * as $dara from '@darabonba/typescript';
import { CreateSmsAuthorizationLetterResponseBody } from "./CreateSmsAuthorizationLetterResponseBody";
export declare class CreateSmsAuthorizationLetterResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: CreateSmsAuthorizationLetterResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
