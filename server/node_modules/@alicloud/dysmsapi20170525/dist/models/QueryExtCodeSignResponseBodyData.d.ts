import * as $dara from '@darabonba/typescript';
import { QueryExtCodeSignResponseBodyDataList } from "./QueryExtCodeSignResponseBodyDataList";
export declare class QueryExtCodeSignResponseBodyData extends $dara.Model {
    list?: QueryExtCodeSignResponseBodyDataList[];
    /**
     * @example
     * 1
     */
    pageNo?: number;
    /**
     * @example
     * 20
     */
    pageSize?: number;
    /**
     * @example
     * 5
     */
    total?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
