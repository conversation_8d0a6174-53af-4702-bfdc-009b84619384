import * as $dara from '@darabonba/typescript';
export declare class QuerySmsAuthorizationLetterShrinkRequest extends $dara.Model {
    /**
     * @remarks
     * 委托授权书id列表
     */
    authorizationLetterIdListShrink?: string;
    /**
     * @remarks
     * 授权方社会统一信用代码
     *
     * @example
     * 9****************A
     */
    organizationCode?: string;
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * 签名名称（支持命中签名范围查询）
     *
     * @example
     * 示例值示例值
     */
    signName?: string;
    /**
     * @remarks
     * 授权书审核状态，INT:审核中，PASSED:审核通过
     *
     * @example
     * PASSED
     */
    state?: string;
    /**
     * @remarks
     * 授权书可用状态，VALID可用，INVALID不可用
     *
     * @example
     * VALID
     */
    status?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
