import * as $dara from '@darabonba/typescript';
import { QuerySmsTemplateListResponseBody } from "./QuerySmsTemplateListResponseBody";
export declare class QuerySmsTemplateListResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QuerySmsTemplateListResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
