import * as $dara from '@darabonba/typescript';
import { QueryMobilesCardSupportResponseBody } from "./QueryMobilesCardSupportResponseBody";
export declare class QueryMobilesCardSupportResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QueryMobilesCardSupportResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
