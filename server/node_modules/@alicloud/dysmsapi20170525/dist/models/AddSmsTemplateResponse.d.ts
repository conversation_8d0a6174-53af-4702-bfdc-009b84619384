import * as $dara from '@darabonba/typescript';
import { AddSmsTemplateResponseBody } from "./AddSmsTemplateResponseBody";
export declare class AddSmsTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: AddSmsTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
