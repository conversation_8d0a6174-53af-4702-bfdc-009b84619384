import * as $dara from '@darabonba/typescript';
import { DeleteShortUrlResponseBody } from "./DeleteShortUrlResponseBody";
export declare class DeleteShortUrlResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: DeleteShortUrlResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
