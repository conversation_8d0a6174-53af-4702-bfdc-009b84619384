import * as $dara from '@darabonba/typescript';
export declare class GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons extends $dara.Model {
    reasonCode?: string;
    reasonDescList?: string[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
