import * as $dara from '@darabonba/typescript';
export declare class DeleteSmsSignResponseBody extends $dara.Model {
    /**
     * @remarks
     * The response code.
     *
     * *   If OK is returned, the request is successful.
     * *   Other values indicate that the request fails. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The returned message.
     *
     * @example
     * OK
     */
    message?: string;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * F655A8D5-B967-440B-8683-DAD6FF8D****
     */
    requestId?: string;
    /**
     * @remarks
     * The signature.
     *
     * @example
     * Aliyun
     */
    signName?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
