import * as $dara from '@darabonba/typescript';
import { SendSmsResponseBody } from "./SendSmsResponseBody";
export declare class SendSmsResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: SendSmsResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
