import * as $dara from '@darabonba/typescript';
export declare class SubmitSmsQualificationRequestOtherFiles extends $dara.Model {
    /**
     * @example
     * 123456/111.png
     */
    licensePic?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
