import * as $dara from '@darabonba/typescript';
import { CheckMobilesCardSupportResponseBody } from "./CheckMobilesCardSupportResponseBody";
export declare class CheckMobilesCardSupportResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: CheckMobilesCardSupportResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
