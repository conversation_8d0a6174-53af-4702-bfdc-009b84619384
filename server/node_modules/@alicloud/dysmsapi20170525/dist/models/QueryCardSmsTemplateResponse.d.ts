import * as $dara from '@darabonba/typescript';
import { QueryCardSmsTemplateResponseBody } from "./QueryCardSmsTemplateResponseBody";
export declare class QueryCardSmsTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QueryCardSmsTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
