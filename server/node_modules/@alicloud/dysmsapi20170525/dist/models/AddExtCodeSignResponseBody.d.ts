import * as $dara from '@darabonba/typescript';
export declare class AddExtCodeSignResponseBody extends $dara.Model {
    accessDeniedDetail?: string;
    /**
     * @example
     * OK
     */
    code?: string;
    /**
     * @example
     * true
     */
    data?: boolean;
    /**
     * @example
     * OK
     */
    message?: string;
    /**
     * @example
     * 90E63D28-E31D-1EB2-8939-A9486641****
     */
    requestId?: string;
    /**
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
