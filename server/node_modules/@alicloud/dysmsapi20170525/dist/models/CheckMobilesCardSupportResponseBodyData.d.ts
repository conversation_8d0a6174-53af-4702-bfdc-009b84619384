import * as $dara from '@darabonba/typescript';
import { CheckMobilesCardSupportResponseBodyDataQueryResult } from "./CheckMobilesCardSupportResponseBodyDataQueryResult";
export declare class CheckMobilesCardSupportResponseBodyData extends $dara.Model {
    /**
     * @remarks
     * The list of returned results.
     */
    queryResult?: CheckMobilesCardSupportResponseBodyDataQueryResult[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
