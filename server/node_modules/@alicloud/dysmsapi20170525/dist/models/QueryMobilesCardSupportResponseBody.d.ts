import * as $dara from '@darabonba/typescript';
import { QueryMobilesCardSupportResponseBodyData } from "./QueryMobilesCardSupportResponseBodyData";
export declare class QueryMobilesCardSupportResponseBody extends $dara.Model {
    /**
     * @remarks
     * The HTTP status code.
     *
     * *   The value OK indicates that the request was successful.
     * *   Other values indicate that the request failed. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The data returned.
     */
    data?: QueryMobilesCardSupportResponseBodyData;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * 08C17DFE-2E10-54F4-BAFB-7180039CC217
     */
    requestId?: string;
    /**
     * @remarks
     * Indicates whether the request is successful. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
