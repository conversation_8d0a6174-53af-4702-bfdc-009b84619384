import * as $dara from '@darabonba/typescript';
import { CreateSmsTemplateResponseBody } from "./CreateSmsTemplateResponseBody";
export declare class CreateSmsTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: CreateSmsTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
