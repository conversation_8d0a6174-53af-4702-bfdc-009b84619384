import * as $dara from '@darabonba/typescript';
import { ListTagResourcesResponseBodyTagResourcesTagResource } from "./ListTagResourcesResponseBodyTagResourcesTagResource";
export declare class ListTagResourcesResponseBodyTagResources extends $dara.Model {
    tagResource?: ListTagResourcesResponseBodyTagResourcesTagResource[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
