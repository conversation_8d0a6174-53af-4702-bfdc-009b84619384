import * as $dara from '@darabonba/typescript';
export declare class QuerySendDetailsRequest extends $dara.Model {
    /**
     * @remarks
     * The ID of the delivery receipt. The delivery receipt ID is the value of the BizId parameter that is returned when you call the SendSms or SendBatchSms operation.
     *
     * @example
     * 134523^435****
     */
    bizId?: string;
    /**
     * @remarks
     * The page number of the first page.
     *
     * This parameter is required.
     *
     * @example
     * 1
     */
    currentPage?: number;
    ownerId?: number;
    /**
     * @remarks
     * The number of items displayed per page.
     *
     * Valid values: 1 to 50.
     *
     * This parameter is required.
     *
     * @example
     * 10
     */
    pageSize?: number;
    /**
     * @remarks
     * The mobile numbers of the recipients. Format:
     *
     * *   If you send messages in the Chinese mainland, specify an 11-digit mobile number, for example, 1390000\\*\\*\\*\\*.
     * *   If you send messages to countries or regions outside the Chinese mainland, specify this parameter in the \\<Area code>\\<Mobile number> format. Example: 8520000\\*\\*\\*\\*.
     *
     * This parameter is required.
     *
     * @example
     * 1390000****
     */
    phoneNumber?: string;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * The date when the message was sent. You can query messages that were sent within the last 30 days.
     *
     * Format: yyyyMMdd. Example: ********.
     *
     * This parameter is required.
     *
     * @example
     * ********
     */
    sendDate?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
