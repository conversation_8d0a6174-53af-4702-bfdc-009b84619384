import * as $dara from '@darabonba/typescript';
export declare class UpdateSmsSignResponseBody extends $dara.Model {
    /**
     * @remarks
     * Request status code.
     *
     * * OK indicates a successful request.
     * * For other error codes, refer to [Error Code List](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * Description of the status code.
     *
     * @example
     * OK
     */
    message?: string;
    /**
     * @remarks
     * Work order number.
     *
     * This parameter is used by auditors when querying audits. You need to provide this work order number for expedited review.
     *
     * @example
     * 2004417****
     */
    orderId?: string;
    /**
     * @remarks
     * The ID of this call request, uniquely generated by Alibaba Cloud, which can be used for troubleshooting and issue localization.
     *
     * @example
     * A90E4451-FED7-49D2-87C8-00700A8C4D0D
     */
    requestId?: string;
    /**
     * @remarks
     * The modified signature name.
     *
     * @example
     * 登录验证
     */
    signName?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
