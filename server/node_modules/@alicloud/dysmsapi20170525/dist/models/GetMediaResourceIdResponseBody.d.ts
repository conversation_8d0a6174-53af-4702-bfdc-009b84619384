import * as $dara from '@darabonba/typescript';
import { GetMediaResourceIdResponseBodyData } from "./GetMediaResourceIdResponseBodyData";
export declare class GetMediaResourceIdResponseBody extends $dara.Model {
    /**
     * @remarks
     * The response code.
     *
     * *   If OK is returned, the request is successful.
     * *   Other values indicate that the request fails. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The data returned.
     */
    data?: GetMediaResourceIdResponseBodyData;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * F07CF237-F6E3-5F77-B91B-F9B7C5DE84AB
     */
    requestId?: string;
    /**
     * @remarks
     * Indicates whether the request was successful. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
