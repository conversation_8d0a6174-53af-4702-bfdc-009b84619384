import * as $dara from '@darabonba/typescript';
export declare class UntagResourcesResponseBody extends $dara.Model {
    /**
     * @remarks
     * The HTTP status code.
     *
     * *   The value OK indicates that the request was successful.
     * *   Other values indicate that the request failed. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * Indicates whether the request is successful. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    data?: string;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * A90E4451-FED7-49D2-87C8-00700A8C****
     */
    requestId?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
