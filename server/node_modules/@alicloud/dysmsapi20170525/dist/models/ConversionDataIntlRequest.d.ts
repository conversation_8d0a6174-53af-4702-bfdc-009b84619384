import * as $dara from '@darabonba/typescript';
export declare class ConversionDataIntlRequest extends $dara.Model {
    /**
     * @remarks
     * The conversion rate.
     *
     * > The value of this parameter is a double, and ranges from 0 to 1.
     *
     * This parameter is required.
     *
     * @example
     * 0.53
     */
    conversionRate?: string;
    ownerId?: number;
    /**
     * @remarks
     * The time point at which the conversion rate is monitored. The value is a UNIX timestamp. Unit: milliseconds.
     *
     * > If you do not specify this parameter, the current timestamp is used by default.
     *
     * @example
     * *************
     */
    reportTime?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
