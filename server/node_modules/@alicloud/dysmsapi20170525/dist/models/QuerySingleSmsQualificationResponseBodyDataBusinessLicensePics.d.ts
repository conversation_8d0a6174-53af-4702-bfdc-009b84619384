import * as $dara from '@darabonba/typescript';
export declare class QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics extends $dara.Model {
    /**
     * @example
     * 123456/111.png
     */
    licensePic?: string;
    /**
     * @remarks
     * 文件的完整路径
     *
     * @example
     * https://******.aliyuncs.com/******
     */
    picUrl?: string;
    /**
     * @example
     * businessLicense
     */
    type?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
