import * as $dara from '@darabonba/typescript';
import { QueryShortUrlResponseBody } from "./QueryShortUrlResponseBody";
export declare class QueryShortUrlResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QueryShortUrlResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
