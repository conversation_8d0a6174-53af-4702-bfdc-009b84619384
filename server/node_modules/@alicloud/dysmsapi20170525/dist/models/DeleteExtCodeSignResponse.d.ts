import * as $dara from '@darabonba/typescript';
import { DeleteExtCodeSignResponseBody } from "./DeleteExtCodeSignResponseBody";
export declare class DeleteExtCodeSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: DeleteExtCodeSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
