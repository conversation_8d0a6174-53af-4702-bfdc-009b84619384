import * as $dara from '@darabonba/typescript';
import { GetSmsSignResponseBodyAuditInfo } from "./GetSmsSignResponseBodyAuditInfo";
import { GetSmsSignResponseBodySignIspRegisterDetailList } from "./GetSmsSignResponseBodySignIspRegisterDetailList";
export declare class GetSmsSignResponseBody extends $dara.Model {
    /**
     * @remarks
     * Content of application scenarios.
     *
     * @example
     * http://www.aliyun.com/
     */
    applyScene?: string;
    /**
     * @remarks
     * Audit information.
     */
    auditInfo?: GetSmsSignResponseBodyAuditInfo;
    authorizationLetterAuditPass?: boolean;
    authorizationLetterId?: number;
    /**
     * @remarks
     * Request status code.
     *
     * - OK indicates a successful request.
     * - For other error codes, see [API Error Codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * Creation date and time of the SMS signature.
     *
     * @example
     * 2024-06-03 10:02:34
     */
    createDate?: string;
    /**
     * @remarks
     * 更多资料信息，补充上传业务证明文件或业务截图文件列表。
     */
    fileUrlList?: string[];
    /**
     * @remarks
     * Description of the status code.
     *
     * @example
     * OK
     */
    message?: string;
    /**
     * @remarks
     * Work order number.
     *
     * Used by reviewers when querying the review. You need to provide this work order number if you require expedited review.
     *
     * @example
     * 20044156924
     */
    orderId?: string;
    /**
     * @remarks
     * Credential ID, the credential ID associated when applying for the signature.
     *
     * @example
     * 2004393****
     */
    qualificationId?: number;
    registerResult?: number;
    /**
     * @remarks
     * Explanation of the SMS signature scenario, with a maximum length of 200 characters.
     *
     * @example
     * Send verification code text message during login.
     */
    remark?: string;
    /**
     * @remarks
     * The ID of this call request, which is a unique identifier generated by Alibaba Cloud for the request and can be used for troubleshooting and issue localization.
     *
     * @example
     * F655A8D5-B967-440B-8683-DAD6FF8DE990
     */
    requestId?: string;
    /**
     * @remarks
     * SMS signature code.
     *
     * @example
     * SIGN_100000077042023_17174665*****_ZM2kG
     */
    signCode?: string;
    signIspRegisterDetailList?: GetSmsSignResponseBodySignIspRegisterDetailList[];
    /**
     * @remarks
     * SMS signature name.
     *
     * @example
     * Aliyun
     */
    signName?: string;
    /**
     * @remarks
     * Signature review status. Values:
     *
     * - **0**: Under review.
     * - **1**: Approved.
     * - **2**: Review failed, please check the Reason parameter for the failure cause.
     * - **10**: Review canceled.
     *
     * @example
     * 2
     */
    signStatus?: number;
    /**
     * @remarks
     * Signature tag indicating whether the signature is user-defined, system-provided, test, or trial. Values:
     *
     * - 2: User-defined signature
     * - 3: System-provided signature
     * - 4: Test signature
     * - 5: Trial signature
     *
     * @example
     * 2
     */
    signTag?: string;
    /**
     * @remarks
     * scenarios for using signatures.
     *
     * @example
     * App.
     */
    signUsage?: string;
    /**
     * @remarks
     * Signature usage indication—self-use or third-party use.
     *
     * - false: Self-use (default)
     *
     * - true: Third-party use
     *
     * @example
     * false
     */
    thirdParty?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
