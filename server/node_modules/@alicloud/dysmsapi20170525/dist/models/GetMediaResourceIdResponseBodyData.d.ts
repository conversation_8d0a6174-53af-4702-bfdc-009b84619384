import * as $dara from '@darabonba/typescript';
export declare class GetMediaResourceIdResponseBodyData extends $dara.Model {
    /**
     * @remarks
     * The download URL of the resource.
     *
     * @example
     * http://test-example.com/download.jpg
     */
    resUrlDownload?: string;
    /**
     * @remarks
     * The resource ID.
     *
     * @example
     * SMS_14571****
     */
    resourceId?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
