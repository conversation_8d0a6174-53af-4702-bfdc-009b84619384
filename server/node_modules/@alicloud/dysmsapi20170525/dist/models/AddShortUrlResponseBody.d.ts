import * as $dara from '@darabonba/typescript';
import { AddShortUrlResponseBodyData } from "./AddShortUrlResponseBodyData";
export declare class AddShortUrlResponseBody extends $dara.Model {
    /**
     * @remarks
     * The response code.
     *
     * *   The value OK indicates that the request was successful.
     * *   Other values indicate that the request failed. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The details of the short URL.
     */
    data?: AddShortUrlResponseBodyData;
    /**
     * @remarks
     * The returned message.
     *
     * @example
     * OK
     */
    message?: string;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * 819BE656-D2E0-4858-8B21-B2E477085AAF
     */
    requestId?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
