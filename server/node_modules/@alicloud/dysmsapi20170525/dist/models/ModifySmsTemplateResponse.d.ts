import * as $dara from '@darabonba/typescript';
import { ModifySmsTemplateResponseBody } from "./ModifySmsTemplateResponseBody";
export declare class ModifySmsTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: ModifySmsTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
