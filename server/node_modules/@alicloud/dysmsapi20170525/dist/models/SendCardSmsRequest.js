"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendCardSmsRequest = void 0;
// This file is auto-generated, don't edit it
const $dara = __importStar(require("@darabonba/typescript"));
const SendCardSmsRequestCardObjects_1 = require("./SendCardSmsRequestCardObjects");
class SendCardSmsRequest extends $dara.Model {
    static names() {
        return {
            cardObjects: 'CardObjects',
            cardTemplateCode: 'CardTemplateCode',
            digitalTemplateCode: 'DigitalTemplateCode',
            digitalTemplateParam: 'DigitalTemplateParam',
            fallbackType: 'FallbackType',
            outId: 'OutId',
            signName: 'SignName',
            smsTemplateCode: 'SmsTemplateCode',
            smsTemplateParam: 'SmsTemplateParam',
            smsUpExtendCode: 'SmsUpExtendCode',
            templateCode: 'TemplateCode',
            templateParam: 'TemplateParam',
        };
    }
    static types() {
        return {
            cardObjects: { 'type': 'array', 'itemType': SendCardSmsRequestCardObjects_1.SendCardSmsRequestCardObjects },
            cardTemplateCode: 'string',
            digitalTemplateCode: 'string',
            digitalTemplateParam: 'string',
            fallbackType: 'string',
            outId: 'string',
            signName: 'string',
            smsTemplateCode: 'string',
            smsTemplateParam: 'string',
            smsUpExtendCode: 'string',
            templateCode: 'string',
            templateParam: 'string',
        };
    }
    validate() {
        if (Array.isArray(this.cardObjects)) {
            $dara.Model.validateArray(this.cardObjects);
        }
        super.validate();
    }
    constructor(map) {
        super(map);
    }
}
exports.SendCardSmsRequest = SendCardSmsRequest;
//# sourceMappingURL=SendCardSmsRequest.js.map