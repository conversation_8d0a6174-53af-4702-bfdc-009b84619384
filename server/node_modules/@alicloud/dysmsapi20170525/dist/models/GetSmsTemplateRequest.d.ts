import * as $dara from '@darabonba/typescript';
export declare class GetSmsTemplateRequest extends $dara.Model {
    ownerId?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * SMS template code.
     *
     * - Obtain the SMS template code from the return parameters of the [CreateSmsTemplate](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-createsmstemplate?spm) API.
     * - View the SMS template code on the [Template Management](https://dysms.console.aliyun.com/domestic/text/template) page.
     *
     * This parameter is required.
     *
     * @example
     * SMS_20375****
     */
    templateCode?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
