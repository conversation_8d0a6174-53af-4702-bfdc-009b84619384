import * as $dara from '@darabonba/typescript';
import { GetCardSmsLinkResponseBody } from "./GetCardSmsLinkResponseBody";
export declare class GetCardSmsLinkResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: GetCardSmsLinkResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
