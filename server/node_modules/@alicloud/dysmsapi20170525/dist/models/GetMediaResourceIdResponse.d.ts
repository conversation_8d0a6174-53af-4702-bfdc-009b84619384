import * as $dara from '@darabonba/typescript';
import { GetMediaResourceIdResponseBody } from "./GetMediaResourceIdResponseBody";
export declare class GetMediaResourceIdResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: GetMediaResourceIdResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
