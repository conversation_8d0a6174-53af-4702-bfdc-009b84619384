"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendCardSmsResponseBodyData = exports.SendCardSmsRequestCardObjects = exports.SendBatchCardSmsResponseBodyData = exports.QuerySmsTemplateListResponseBodySmsTemplateList = exports.QuerySmsTemplateListResponseBodySmsTemplateListReason = exports.QuerySmsSignListResponseBodySmsSignList = exports.QuerySmsSignListResponseBodySmsSignListReason = exports.QuerySmsQualificationRecordResponseBodyData = exports.QuerySmsQualificationRecordResponseBodyDataList = exports.QuerySmsAuthorizationLetterResponseBodyData = exports.QuerySingleSmsQualificationResponseBodyData = exports.QuerySingleSmsQualificationResponseBodyDataOtherFiles = exports.QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics = exports.QueryShortUrlResponseBodyData = exports.QuerySendStatisticsResponseBodyData = exports.QuerySendStatisticsResponseBodyDataTargetList = exports.QuerySendDetailsResponseBodySmsSendDetailDTOs = exports.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO = exports.QueryPageSmartShortUrlLogResponseBodyModel = exports.QueryPageSmartShortUrlLogResponseBodyModelList = exports.QueryMobilesCardSupportResponseBodyData = exports.QueryMobilesCardSupportResponseBodyDataQueryResult = exports.QueryExtCodeSignResponseBodyData = exports.QueryExtCodeSignResponseBodyDataList = exports.QueryCardSmsTemplateReportResponseBodyData = exports.QueryCardSmsTemplateResponseBodyData = exports.ModifySmsSignRequestSignFileList = exports.ListTagResourcesResponseBodyTagResources = exports.ListTagResourcesResponseBodyTagResourcesTagResource = exports.ListTagResourcesRequestTag = exports.GetSmsTemplateResponseBodyMoreDataFileUrlList = exports.GetSmsTemplateResponseBodyFileUrlList = exports.GetSmsTemplateResponseBodyAuditInfo = exports.GetSmsSignResponseBodySignIspRegisterDetailList = exports.GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons = exports.GetSmsSignResponseBodyAuditInfo = exports.GetQualificationOssInfoResponseBodyData = exports.GetOSSInfoForUploadFileResponseBodyModel = exports.GetOSSInfoForCardTemplateResponseBodyData = exports.GetMediaResourceIdResponseBodyData = exports.GetCardSmsLinkResponseBodyData = exports.GetCardSmsDetailsResponseBodyCardSendDetailDTO = exports.GetCardSmsDetailsResponseBodyCardSendDetailDTORecords = exports.CreateSmartShortUrlResponseBodyModel = exports.CreateCardSmsTemplateResponseBodyData = exports.CheckMobilesCardSupportResponseBodyData = exports.CheckMobilesCardSupportResponseBodyDataQueryResult = exports.ChangeSignatureQualificationResponseBodyData = exports.AddSmsSignRequestSignFileList = exports.AddShortUrlResponseBodyData = void 0;
exports.DeleteShortUrlResponseBody = exports.DeleteShortUrlRequest = exports.DeleteExtCodeSignResponse = exports.DeleteExtCodeSignResponseBody = exports.DeleteExtCodeSignRequest = exports.CreateSmsTemplateResponse = exports.CreateSmsTemplateResponseBody = exports.CreateSmsTemplateShrinkRequest = exports.CreateSmsTemplateRequest = exports.CreateSmsSignResponse = exports.CreateSmsSignResponseBody = exports.CreateSmsSignShrinkRequest = exports.CreateSmsSignRequest = exports.CreateSmsAuthorizationLetterResponse = exports.CreateSmsAuthorizationLetterResponseBody = exports.CreateSmsAuthorizationLetterShrinkRequest = exports.CreateSmsAuthorizationLetterRequest = exports.CreateSmartShortUrlResponse = exports.CreateSmartShortUrlResponseBody = exports.CreateSmartShortUrlRequest = exports.CreateCardSmsTemplateResponse = exports.CreateCardSmsTemplateResponseBody = exports.CreateCardSmsTemplateShrinkRequest = exports.CreateCardSmsTemplateRequest = exports.ConversionDataIntlResponse = exports.ConversionDataIntlResponseBody = exports.ConversionDataIntlRequest = exports.CheckMobilesCardSupportResponse = exports.CheckMobilesCardSupportResponseBody = exports.CheckMobilesCardSupportRequest = exports.ChangeSignatureQualificationResponse = exports.ChangeSignatureQualificationResponseBody = exports.ChangeSignatureQualificationRequest = exports.AddSmsTemplateResponse = exports.AddSmsTemplateResponseBody = exports.AddSmsTemplateRequest = exports.AddSmsSignResponse = exports.AddSmsSignResponseBody = exports.AddSmsSignRequest = exports.AddShortUrlResponse = exports.AddShortUrlResponseBody = exports.AddShortUrlRequest = exports.AddExtCodeSignResponse = exports.AddExtCodeSignResponseBody = exports.AddExtCodeSignRequest = exports.UpdateSmsQualificationRequestOtherFiles = exports.UpdateSmsQualificationRequestBusinessLicensePics = exports.TagResourcesRequestTag = exports.SubmitSmsQualificationRequestOtherFiles = exports.SubmitSmsQualificationRequestBusinessLicensePics = void 0;
exports.QueryExtCodeSignResponseBody = exports.QueryExtCodeSignRequest = exports.QueryCardSmsTemplateReportResponse = exports.QueryCardSmsTemplateReportResponseBody = exports.QueryCardSmsTemplateReportRequest = exports.QueryCardSmsTemplateResponse = exports.QueryCardSmsTemplateResponseBody = exports.QueryCardSmsTemplateRequest = exports.ModifySmsTemplateResponse = exports.ModifySmsTemplateResponseBody = exports.ModifySmsTemplateRequest = exports.ModifySmsSignResponse = exports.ModifySmsSignResponseBody = exports.ModifySmsSignRequest = exports.ListTagResourcesResponse = exports.ListTagResourcesResponseBody = exports.ListTagResourcesRequest = exports.GetSmsTemplateResponse = exports.GetSmsTemplateResponseBody = exports.GetSmsTemplateRequest = exports.GetSmsSignResponse = exports.GetSmsSignResponseBody = exports.GetSmsSignRequest = exports.GetQualificationOssInfoResponse = exports.GetQualificationOssInfoResponseBody = exports.GetQualificationOssInfoRequest = exports.GetOSSInfoForUploadFileResponse = exports.GetOSSInfoForUploadFileResponseBody = exports.GetOSSInfoForUploadFileRequest = exports.GetOSSInfoForCardTemplateResponse = exports.GetOSSInfoForCardTemplateResponseBody = exports.GetMediaResourceIdResponse = exports.GetMediaResourceIdResponseBody = exports.GetMediaResourceIdRequest = exports.GetCardSmsLinkResponse = exports.GetCardSmsLinkResponseBody = exports.GetCardSmsLinkRequest = exports.GetCardSmsDetailsResponse = exports.GetCardSmsDetailsResponseBody = exports.GetCardSmsDetailsRequest = exports.DeleteSmsTemplateResponse = exports.DeleteSmsTemplateResponseBody = exports.DeleteSmsTemplateRequest = exports.DeleteSmsSignResponse = exports.DeleteSmsSignResponseBody = exports.DeleteSmsSignRequest = exports.DeleteSmsQualificationResponse = exports.DeleteSmsQualificationResponseBody = exports.DeleteSmsQualificationRequest = exports.DeleteShortUrlResponse = void 0;
exports.SendCardSmsResponseBody = exports.SendCardSmsRequest = exports.SendBatchSmsResponse = exports.SendBatchSmsResponseBody = exports.SendBatchSmsRequest = exports.SendBatchCardSmsResponse = exports.SendBatchCardSmsResponseBody = exports.SendBatchCardSmsRequest = exports.RequiredPhoneCodeResponse = exports.RequiredPhoneCodeResponseBody = exports.RequiredPhoneCodeRequest = exports.QuerySmsTemplateListResponse = exports.QuerySmsTemplateListResponseBody = exports.QuerySmsTemplateListRequest = exports.QuerySmsTemplateResponse = exports.QuerySmsTemplateResponseBody = exports.QuerySmsTemplateRequest = exports.QuerySmsSignListResponse = exports.QuerySmsSignListResponseBody = exports.QuerySmsSignListRequest = exports.QuerySmsSignResponse = exports.QuerySmsSignResponseBody = exports.QuerySmsSignRequest = exports.QuerySmsQualificationRecordResponse = exports.QuerySmsQualificationRecordResponseBody = exports.QuerySmsQualificationRecordRequest = exports.QuerySmsAuthorizationLetterResponse = exports.QuerySmsAuthorizationLetterResponseBody = exports.QuerySmsAuthorizationLetterShrinkRequest = exports.QuerySmsAuthorizationLetterRequest = exports.QuerySingleSmsQualificationResponse = exports.QuerySingleSmsQualificationResponseBody = exports.QuerySingleSmsQualificationRequest = exports.QueryShortUrlResponse = exports.QueryShortUrlResponseBody = exports.QueryShortUrlRequest = exports.QuerySendStatisticsResponse = exports.QuerySendStatisticsResponseBody = exports.QuerySendStatisticsRequest = exports.QuerySendDetailsResponse = exports.QuerySendDetailsResponseBody = exports.QuerySendDetailsRequest = exports.QueryPageSmartShortUrlLogResponse = exports.QueryPageSmartShortUrlLogResponseBody = exports.QueryPageSmartShortUrlLogRequest = exports.QueryMobilesCardSupportResponse = exports.QueryMobilesCardSupportResponseBody = exports.QueryMobilesCardSupportShrinkRequest = exports.QueryMobilesCardSupportRequest = exports.QueryExtCodeSignResponse = void 0;
exports.ValidPhoneCodeResponse = exports.ValidPhoneCodeResponseBody = exports.ValidPhoneCodeRequest = exports.UpdateSmsTemplateResponse = exports.UpdateSmsTemplateResponseBody = exports.UpdateSmsTemplateShrinkRequest = exports.UpdateSmsTemplateRequest = exports.UpdateSmsSignResponse = exports.UpdateSmsSignResponseBody = exports.UpdateSmsSignShrinkRequest = exports.UpdateSmsSignRequest = exports.UpdateSmsQualificationResponse = exports.UpdateSmsQualificationResponseBody = exports.UpdateSmsQualificationShrinkRequest = exports.UpdateSmsQualificationRequest = exports.UpdateExtCodeSignResponse = exports.UpdateExtCodeSignResponseBody = exports.UpdateExtCodeSignRequest = exports.UntagResourcesResponse = exports.UntagResourcesResponseBody = exports.UntagResourcesRequest = exports.TagResourcesResponse = exports.TagResourcesResponseBody = exports.TagResourcesRequest = exports.SubmitSmsQualificationResponse = exports.SubmitSmsQualificationResponseBody = exports.SubmitSmsQualificationShrinkRequest = exports.SubmitSmsQualificationRequest = exports.SmsConversionIntlResponse = exports.SmsConversionIntlResponseBody = exports.SmsConversionIntlRequest = exports.SendSmsResponse = exports.SendSmsResponseBody = exports.SendSmsRequest = exports.SendCardSmsResponse = void 0;
var AddShortUrlResponseBodyData_1 = require("./AddShortUrlResponseBodyData");
Object.defineProperty(exports, "AddShortUrlResponseBodyData", { enumerable: true, get: function () { return AddShortUrlResponseBodyData_1.AddShortUrlResponseBodyData; } });
var AddSmsSignRequestSignFileList_1 = require("./AddSmsSignRequestSignFileList");
Object.defineProperty(exports, "AddSmsSignRequestSignFileList", { enumerable: true, get: function () { return AddSmsSignRequestSignFileList_1.AddSmsSignRequestSignFileList; } });
var ChangeSignatureQualificationResponseBodyData_1 = require("./ChangeSignatureQualificationResponseBodyData");
Object.defineProperty(exports, "ChangeSignatureQualificationResponseBodyData", { enumerable: true, get: function () { return ChangeSignatureQualificationResponseBodyData_1.ChangeSignatureQualificationResponseBodyData; } });
var CheckMobilesCardSupportResponseBodyDataQueryResult_1 = require("./CheckMobilesCardSupportResponseBodyDataQueryResult");
Object.defineProperty(exports, "CheckMobilesCardSupportResponseBodyDataQueryResult", { enumerable: true, get: function () { return CheckMobilesCardSupportResponseBodyDataQueryResult_1.CheckMobilesCardSupportResponseBodyDataQueryResult; } });
var CheckMobilesCardSupportResponseBodyData_1 = require("./CheckMobilesCardSupportResponseBodyData");
Object.defineProperty(exports, "CheckMobilesCardSupportResponseBodyData", { enumerable: true, get: function () { return CheckMobilesCardSupportResponseBodyData_1.CheckMobilesCardSupportResponseBodyData; } });
var CreateCardSmsTemplateResponseBodyData_1 = require("./CreateCardSmsTemplateResponseBodyData");
Object.defineProperty(exports, "CreateCardSmsTemplateResponseBodyData", { enumerable: true, get: function () { return CreateCardSmsTemplateResponseBodyData_1.CreateCardSmsTemplateResponseBodyData; } });
var CreateSmartShortUrlResponseBodyModel_1 = require("./CreateSmartShortUrlResponseBodyModel");
Object.defineProperty(exports, "CreateSmartShortUrlResponseBodyModel", { enumerable: true, get: function () { return CreateSmartShortUrlResponseBodyModel_1.CreateSmartShortUrlResponseBodyModel; } });
var GetCardSmsDetailsResponseBodyCardSendDetailDtorecords_1 = require("./GetCardSmsDetailsResponseBodyCardSendDetailDtorecords");
Object.defineProperty(exports, "GetCardSmsDetailsResponseBodyCardSendDetailDTORecords", { enumerable: true, get: function () { return GetCardSmsDetailsResponseBodyCardSendDetailDtorecords_1.GetCardSmsDetailsResponseBodyCardSendDetailDTORecords; } });
var GetCardSmsDetailsResponseBodyCardSendDetailDto_1 = require("./GetCardSmsDetailsResponseBodyCardSendDetailDto");
Object.defineProperty(exports, "GetCardSmsDetailsResponseBodyCardSendDetailDTO", { enumerable: true, get: function () { return GetCardSmsDetailsResponseBodyCardSendDetailDto_1.GetCardSmsDetailsResponseBodyCardSendDetailDTO; } });
var GetCardSmsLinkResponseBodyData_1 = require("./GetCardSmsLinkResponseBodyData");
Object.defineProperty(exports, "GetCardSmsLinkResponseBodyData", { enumerable: true, get: function () { return GetCardSmsLinkResponseBodyData_1.GetCardSmsLinkResponseBodyData; } });
var GetMediaResourceIdResponseBodyData_1 = require("./GetMediaResourceIdResponseBodyData");
Object.defineProperty(exports, "GetMediaResourceIdResponseBodyData", { enumerable: true, get: function () { return GetMediaResourceIdResponseBodyData_1.GetMediaResourceIdResponseBodyData; } });
var GetOssinfoForCardTemplateResponseBodyData_1 = require("./GetOssinfoForCardTemplateResponseBodyData");
Object.defineProperty(exports, "GetOSSInfoForCardTemplateResponseBodyData", { enumerable: true, get: function () { return GetOssinfoForCardTemplateResponseBodyData_1.GetOSSInfoForCardTemplateResponseBodyData; } });
var GetOssinfoForUploadFileResponseBodyModel_1 = require("./GetOssinfoForUploadFileResponseBodyModel");
Object.defineProperty(exports, "GetOSSInfoForUploadFileResponseBodyModel", { enumerable: true, get: function () { return GetOssinfoForUploadFileResponseBodyModel_1.GetOSSInfoForUploadFileResponseBodyModel; } });
var GetQualificationOssInfoResponseBodyData_1 = require("./GetQualificationOssInfoResponseBodyData");
Object.defineProperty(exports, "GetQualificationOssInfoResponseBodyData", { enumerable: true, get: function () { return GetQualificationOssInfoResponseBodyData_1.GetQualificationOssInfoResponseBodyData; } });
var GetSmsSignResponseBodyAuditInfo_1 = require("./GetSmsSignResponseBodyAuditInfo");
Object.defineProperty(exports, "GetSmsSignResponseBodyAuditInfo", { enumerable: true, get: function () { return GetSmsSignResponseBodyAuditInfo_1.GetSmsSignResponseBodyAuditInfo; } });
var GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons_1 = require("./GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons");
Object.defineProperty(exports, "GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons", { enumerable: true, get: function () { return GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons_1.GetSmsSignResponseBodySignIspRegisterDetailListRegisterStatusReasons; } });
var GetSmsSignResponseBodySignIspRegisterDetailList_1 = require("./GetSmsSignResponseBodySignIspRegisterDetailList");
Object.defineProperty(exports, "GetSmsSignResponseBodySignIspRegisterDetailList", { enumerable: true, get: function () { return GetSmsSignResponseBodySignIspRegisterDetailList_1.GetSmsSignResponseBodySignIspRegisterDetailList; } });
var GetSmsTemplateResponseBodyAuditInfo_1 = require("./GetSmsTemplateResponseBodyAuditInfo");
Object.defineProperty(exports, "GetSmsTemplateResponseBodyAuditInfo", { enumerable: true, get: function () { return GetSmsTemplateResponseBodyAuditInfo_1.GetSmsTemplateResponseBodyAuditInfo; } });
var GetSmsTemplateResponseBodyFileUrlList_1 = require("./GetSmsTemplateResponseBodyFileUrlList");
Object.defineProperty(exports, "GetSmsTemplateResponseBodyFileUrlList", { enumerable: true, get: function () { return GetSmsTemplateResponseBodyFileUrlList_1.GetSmsTemplateResponseBodyFileUrlList; } });
var GetSmsTemplateResponseBodyMoreDataFileUrlList_1 = require("./GetSmsTemplateResponseBodyMoreDataFileUrlList");
Object.defineProperty(exports, "GetSmsTemplateResponseBodyMoreDataFileUrlList", { enumerable: true, get: function () { return GetSmsTemplateResponseBodyMoreDataFileUrlList_1.GetSmsTemplateResponseBodyMoreDataFileUrlList; } });
var ListTagResourcesRequestTag_1 = require("./ListTagResourcesRequestTag");
Object.defineProperty(exports, "ListTagResourcesRequestTag", { enumerable: true, get: function () { return ListTagResourcesRequestTag_1.ListTagResourcesRequestTag; } });
var ListTagResourcesResponseBodyTagResourcesTagResource_1 = require("./ListTagResourcesResponseBodyTagResourcesTagResource");
Object.defineProperty(exports, "ListTagResourcesResponseBodyTagResourcesTagResource", { enumerable: true, get: function () { return ListTagResourcesResponseBodyTagResourcesTagResource_1.ListTagResourcesResponseBodyTagResourcesTagResource; } });
var ListTagResourcesResponseBodyTagResources_1 = require("./ListTagResourcesResponseBodyTagResources");
Object.defineProperty(exports, "ListTagResourcesResponseBodyTagResources", { enumerable: true, get: function () { return ListTagResourcesResponseBodyTagResources_1.ListTagResourcesResponseBodyTagResources; } });
var ModifySmsSignRequestSignFileList_1 = require("./ModifySmsSignRequestSignFileList");
Object.defineProperty(exports, "ModifySmsSignRequestSignFileList", { enumerable: true, get: function () { return ModifySmsSignRequestSignFileList_1.ModifySmsSignRequestSignFileList; } });
var QueryCardSmsTemplateResponseBodyData_1 = require("./QueryCardSmsTemplateResponseBodyData");
Object.defineProperty(exports, "QueryCardSmsTemplateResponseBodyData", { enumerable: true, get: function () { return QueryCardSmsTemplateResponseBodyData_1.QueryCardSmsTemplateResponseBodyData; } });
var QueryCardSmsTemplateReportResponseBodyData_1 = require("./QueryCardSmsTemplateReportResponseBodyData");
Object.defineProperty(exports, "QueryCardSmsTemplateReportResponseBodyData", { enumerable: true, get: function () { return QueryCardSmsTemplateReportResponseBodyData_1.QueryCardSmsTemplateReportResponseBodyData; } });
var QueryExtCodeSignResponseBodyDataList_1 = require("./QueryExtCodeSignResponseBodyDataList");
Object.defineProperty(exports, "QueryExtCodeSignResponseBodyDataList", { enumerable: true, get: function () { return QueryExtCodeSignResponseBodyDataList_1.QueryExtCodeSignResponseBodyDataList; } });
var QueryExtCodeSignResponseBodyData_1 = require("./QueryExtCodeSignResponseBodyData");
Object.defineProperty(exports, "QueryExtCodeSignResponseBodyData", { enumerable: true, get: function () { return QueryExtCodeSignResponseBodyData_1.QueryExtCodeSignResponseBodyData; } });
var QueryMobilesCardSupportResponseBodyDataQueryResult_1 = require("./QueryMobilesCardSupportResponseBodyDataQueryResult");
Object.defineProperty(exports, "QueryMobilesCardSupportResponseBodyDataQueryResult", { enumerable: true, get: function () { return QueryMobilesCardSupportResponseBodyDataQueryResult_1.QueryMobilesCardSupportResponseBodyDataQueryResult; } });
var QueryMobilesCardSupportResponseBodyData_1 = require("./QueryMobilesCardSupportResponseBodyData");
Object.defineProperty(exports, "QueryMobilesCardSupportResponseBodyData", { enumerable: true, get: function () { return QueryMobilesCardSupportResponseBodyData_1.QueryMobilesCardSupportResponseBodyData; } });
var QueryPageSmartShortUrlLogResponseBodyModelList_1 = require("./QueryPageSmartShortUrlLogResponseBodyModelList");
Object.defineProperty(exports, "QueryPageSmartShortUrlLogResponseBodyModelList", { enumerable: true, get: function () { return QueryPageSmartShortUrlLogResponseBodyModelList_1.QueryPageSmartShortUrlLogResponseBodyModelList; } });
var QueryPageSmartShortUrlLogResponseBodyModel_1 = require("./QueryPageSmartShortUrlLogResponseBodyModel");
Object.defineProperty(exports, "QueryPageSmartShortUrlLogResponseBodyModel", { enumerable: true, get: function () { return QueryPageSmartShortUrlLogResponseBodyModel_1.QueryPageSmartShortUrlLogResponseBodyModel; } });
var QuerySendDetailsResponseBodySmsSendDetailDtosSmsSendDetailDto_1 = require("./QuerySendDetailsResponseBodySmsSendDetailDtosSmsSendDetailDto");
Object.defineProperty(exports, "QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO", { enumerable: true, get: function () { return QuerySendDetailsResponseBodySmsSendDetailDtosSmsSendDetailDto_1.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO; } });
var QuerySendDetailsResponseBodySmsSendDetailDtos_1 = require("./QuerySendDetailsResponseBodySmsSendDetailDtos");
Object.defineProperty(exports, "QuerySendDetailsResponseBodySmsSendDetailDTOs", { enumerable: true, get: function () { return QuerySendDetailsResponseBodySmsSendDetailDtos_1.QuerySendDetailsResponseBodySmsSendDetailDTOs; } });
var QuerySendStatisticsResponseBodyDataTargetList_1 = require("./QuerySendStatisticsResponseBodyDataTargetList");
Object.defineProperty(exports, "QuerySendStatisticsResponseBodyDataTargetList", { enumerable: true, get: function () { return QuerySendStatisticsResponseBodyDataTargetList_1.QuerySendStatisticsResponseBodyDataTargetList; } });
var QuerySendStatisticsResponseBodyData_1 = require("./QuerySendStatisticsResponseBodyData");
Object.defineProperty(exports, "QuerySendStatisticsResponseBodyData", { enumerable: true, get: function () { return QuerySendStatisticsResponseBodyData_1.QuerySendStatisticsResponseBodyData; } });
var QueryShortUrlResponseBodyData_1 = require("./QueryShortUrlResponseBodyData");
Object.defineProperty(exports, "QueryShortUrlResponseBodyData", { enumerable: true, get: function () { return QueryShortUrlResponseBodyData_1.QueryShortUrlResponseBodyData; } });
var QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics_1 = require("./QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics");
Object.defineProperty(exports, "QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics", { enumerable: true, get: function () { return QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics_1.QuerySingleSmsQualificationResponseBodyDataBusinessLicensePics; } });
var QuerySingleSmsQualificationResponseBodyDataOtherFiles_1 = require("./QuerySingleSmsQualificationResponseBodyDataOtherFiles");
Object.defineProperty(exports, "QuerySingleSmsQualificationResponseBodyDataOtherFiles", { enumerable: true, get: function () { return QuerySingleSmsQualificationResponseBodyDataOtherFiles_1.QuerySingleSmsQualificationResponseBodyDataOtherFiles; } });
var QuerySingleSmsQualificationResponseBodyData_1 = require("./QuerySingleSmsQualificationResponseBodyData");
Object.defineProperty(exports, "QuerySingleSmsQualificationResponseBodyData", { enumerable: true, get: function () { return QuerySingleSmsQualificationResponseBodyData_1.QuerySingleSmsQualificationResponseBodyData; } });
var QuerySmsAuthorizationLetterResponseBodyData_1 = require("./QuerySmsAuthorizationLetterResponseBodyData");
Object.defineProperty(exports, "QuerySmsAuthorizationLetterResponseBodyData", { enumerable: true, get: function () { return QuerySmsAuthorizationLetterResponseBodyData_1.QuerySmsAuthorizationLetterResponseBodyData; } });
var QuerySmsQualificationRecordResponseBodyDataList_1 = require("./QuerySmsQualificationRecordResponseBodyDataList");
Object.defineProperty(exports, "QuerySmsQualificationRecordResponseBodyDataList", { enumerable: true, get: function () { return QuerySmsQualificationRecordResponseBodyDataList_1.QuerySmsQualificationRecordResponseBodyDataList; } });
var QuerySmsQualificationRecordResponseBodyData_1 = require("./QuerySmsQualificationRecordResponseBodyData");
Object.defineProperty(exports, "QuerySmsQualificationRecordResponseBodyData", { enumerable: true, get: function () { return QuerySmsQualificationRecordResponseBodyData_1.QuerySmsQualificationRecordResponseBodyData; } });
var QuerySmsSignListResponseBodySmsSignListReason_1 = require("./QuerySmsSignListResponseBodySmsSignListReason");
Object.defineProperty(exports, "QuerySmsSignListResponseBodySmsSignListReason", { enumerable: true, get: function () { return QuerySmsSignListResponseBodySmsSignListReason_1.QuerySmsSignListResponseBodySmsSignListReason; } });
var QuerySmsSignListResponseBodySmsSignList_1 = require("./QuerySmsSignListResponseBodySmsSignList");
Object.defineProperty(exports, "QuerySmsSignListResponseBodySmsSignList", { enumerable: true, get: function () { return QuerySmsSignListResponseBodySmsSignList_1.QuerySmsSignListResponseBodySmsSignList; } });
var QuerySmsTemplateListResponseBodySmsTemplateListReason_1 = require("./QuerySmsTemplateListResponseBodySmsTemplateListReason");
Object.defineProperty(exports, "QuerySmsTemplateListResponseBodySmsTemplateListReason", { enumerable: true, get: function () { return QuerySmsTemplateListResponseBodySmsTemplateListReason_1.QuerySmsTemplateListResponseBodySmsTemplateListReason; } });
var QuerySmsTemplateListResponseBodySmsTemplateList_1 = require("./QuerySmsTemplateListResponseBodySmsTemplateList");
Object.defineProperty(exports, "QuerySmsTemplateListResponseBodySmsTemplateList", { enumerable: true, get: function () { return QuerySmsTemplateListResponseBodySmsTemplateList_1.QuerySmsTemplateListResponseBodySmsTemplateList; } });
var SendBatchCardSmsResponseBodyData_1 = require("./SendBatchCardSmsResponseBodyData");
Object.defineProperty(exports, "SendBatchCardSmsResponseBodyData", { enumerable: true, get: function () { return SendBatchCardSmsResponseBodyData_1.SendBatchCardSmsResponseBodyData; } });
var SendCardSmsRequestCardObjects_1 = require("./SendCardSmsRequestCardObjects");
Object.defineProperty(exports, "SendCardSmsRequestCardObjects", { enumerable: true, get: function () { return SendCardSmsRequestCardObjects_1.SendCardSmsRequestCardObjects; } });
var SendCardSmsResponseBodyData_1 = require("./SendCardSmsResponseBodyData");
Object.defineProperty(exports, "SendCardSmsResponseBodyData", { enumerable: true, get: function () { return SendCardSmsResponseBodyData_1.SendCardSmsResponseBodyData; } });
var SubmitSmsQualificationRequestBusinessLicensePics_1 = require("./SubmitSmsQualificationRequestBusinessLicensePics");
Object.defineProperty(exports, "SubmitSmsQualificationRequestBusinessLicensePics", { enumerable: true, get: function () { return SubmitSmsQualificationRequestBusinessLicensePics_1.SubmitSmsQualificationRequestBusinessLicensePics; } });
var SubmitSmsQualificationRequestOtherFiles_1 = require("./SubmitSmsQualificationRequestOtherFiles");
Object.defineProperty(exports, "SubmitSmsQualificationRequestOtherFiles", { enumerable: true, get: function () { return SubmitSmsQualificationRequestOtherFiles_1.SubmitSmsQualificationRequestOtherFiles; } });
var TagResourcesRequestTag_1 = require("./TagResourcesRequestTag");
Object.defineProperty(exports, "TagResourcesRequestTag", { enumerable: true, get: function () { return TagResourcesRequestTag_1.TagResourcesRequestTag; } });
var UpdateSmsQualificationRequestBusinessLicensePics_1 = require("./UpdateSmsQualificationRequestBusinessLicensePics");
Object.defineProperty(exports, "UpdateSmsQualificationRequestBusinessLicensePics", { enumerable: true, get: function () { return UpdateSmsQualificationRequestBusinessLicensePics_1.UpdateSmsQualificationRequestBusinessLicensePics; } });
var UpdateSmsQualificationRequestOtherFiles_1 = require("./UpdateSmsQualificationRequestOtherFiles");
Object.defineProperty(exports, "UpdateSmsQualificationRequestOtherFiles", { enumerable: true, get: function () { return UpdateSmsQualificationRequestOtherFiles_1.UpdateSmsQualificationRequestOtherFiles; } });
var AddExtCodeSignRequest_1 = require("./AddExtCodeSignRequest");
Object.defineProperty(exports, "AddExtCodeSignRequest", { enumerable: true, get: function () { return AddExtCodeSignRequest_1.AddExtCodeSignRequest; } });
var AddExtCodeSignResponseBody_1 = require("./AddExtCodeSignResponseBody");
Object.defineProperty(exports, "AddExtCodeSignResponseBody", { enumerable: true, get: function () { return AddExtCodeSignResponseBody_1.AddExtCodeSignResponseBody; } });
var AddExtCodeSignResponse_1 = require("./AddExtCodeSignResponse");
Object.defineProperty(exports, "AddExtCodeSignResponse", { enumerable: true, get: function () { return AddExtCodeSignResponse_1.AddExtCodeSignResponse; } });
var AddShortUrlRequest_1 = require("./AddShortUrlRequest");
Object.defineProperty(exports, "AddShortUrlRequest", { enumerable: true, get: function () { return AddShortUrlRequest_1.AddShortUrlRequest; } });
var AddShortUrlResponseBody_1 = require("./AddShortUrlResponseBody");
Object.defineProperty(exports, "AddShortUrlResponseBody", { enumerable: true, get: function () { return AddShortUrlResponseBody_1.AddShortUrlResponseBody; } });
var AddShortUrlResponse_1 = require("./AddShortUrlResponse");
Object.defineProperty(exports, "AddShortUrlResponse", { enumerable: true, get: function () { return AddShortUrlResponse_1.AddShortUrlResponse; } });
var AddSmsSignRequest_1 = require("./AddSmsSignRequest");
Object.defineProperty(exports, "AddSmsSignRequest", { enumerable: true, get: function () { return AddSmsSignRequest_1.AddSmsSignRequest; } });
var AddSmsSignResponseBody_1 = require("./AddSmsSignResponseBody");
Object.defineProperty(exports, "AddSmsSignResponseBody", { enumerable: true, get: function () { return AddSmsSignResponseBody_1.AddSmsSignResponseBody; } });
var AddSmsSignResponse_1 = require("./AddSmsSignResponse");
Object.defineProperty(exports, "AddSmsSignResponse", { enumerable: true, get: function () { return AddSmsSignResponse_1.AddSmsSignResponse; } });
var AddSmsTemplateRequest_1 = require("./AddSmsTemplateRequest");
Object.defineProperty(exports, "AddSmsTemplateRequest", { enumerable: true, get: function () { return AddSmsTemplateRequest_1.AddSmsTemplateRequest; } });
var AddSmsTemplateResponseBody_1 = require("./AddSmsTemplateResponseBody");
Object.defineProperty(exports, "AddSmsTemplateResponseBody", { enumerable: true, get: function () { return AddSmsTemplateResponseBody_1.AddSmsTemplateResponseBody; } });
var AddSmsTemplateResponse_1 = require("./AddSmsTemplateResponse");
Object.defineProperty(exports, "AddSmsTemplateResponse", { enumerable: true, get: function () { return AddSmsTemplateResponse_1.AddSmsTemplateResponse; } });
var ChangeSignatureQualificationRequest_1 = require("./ChangeSignatureQualificationRequest");
Object.defineProperty(exports, "ChangeSignatureQualificationRequest", { enumerable: true, get: function () { return ChangeSignatureQualificationRequest_1.ChangeSignatureQualificationRequest; } });
var ChangeSignatureQualificationResponseBody_1 = require("./ChangeSignatureQualificationResponseBody");
Object.defineProperty(exports, "ChangeSignatureQualificationResponseBody", { enumerable: true, get: function () { return ChangeSignatureQualificationResponseBody_1.ChangeSignatureQualificationResponseBody; } });
var ChangeSignatureQualificationResponse_1 = require("./ChangeSignatureQualificationResponse");
Object.defineProperty(exports, "ChangeSignatureQualificationResponse", { enumerable: true, get: function () { return ChangeSignatureQualificationResponse_1.ChangeSignatureQualificationResponse; } });
var CheckMobilesCardSupportRequest_1 = require("./CheckMobilesCardSupportRequest");
Object.defineProperty(exports, "CheckMobilesCardSupportRequest", { enumerable: true, get: function () { return CheckMobilesCardSupportRequest_1.CheckMobilesCardSupportRequest; } });
var CheckMobilesCardSupportResponseBody_1 = require("./CheckMobilesCardSupportResponseBody");
Object.defineProperty(exports, "CheckMobilesCardSupportResponseBody", { enumerable: true, get: function () { return CheckMobilesCardSupportResponseBody_1.CheckMobilesCardSupportResponseBody; } });
var CheckMobilesCardSupportResponse_1 = require("./CheckMobilesCardSupportResponse");
Object.defineProperty(exports, "CheckMobilesCardSupportResponse", { enumerable: true, get: function () { return CheckMobilesCardSupportResponse_1.CheckMobilesCardSupportResponse; } });
var ConversionDataIntlRequest_1 = require("./ConversionDataIntlRequest");
Object.defineProperty(exports, "ConversionDataIntlRequest", { enumerable: true, get: function () { return ConversionDataIntlRequest_1.ConversionDataIntlRequest; } });
var ConversionDataIntlResponseBody_1 = require("./ConversionDataIntlResponseBody");
Object.defineProperty(exports, "ConversionDataIntlResponseBody", { enumerable: true, get: function () { return ConversionDataIntlResponseBody_1.ConversionDataIntlResponseBody; } });
var ConversionDataIntlResponse_1 = require("./ConversionDataIntlResponse");
Object.defineProperty(exports, "ConversionDataIntlResponse", { enumerable: true, get: function () { return ConversionDataIntlResponse_1.ConversionDataIntlResponse; } });
var CreateCardSmsTemplateRequest_1 = require("./CreateCardSmsTemplateRequest");
Object.defineProperty(exports, "CreateCardSmsTemplateRequest", { enumerable: true, get: function () { return CreateCardSmsTemplateRequest_1.CreateCardSmsTemplateRequest; } });
var CreateCardSmsTemplateShrinkRequest_1 = require("./CreateCardSmsTemplateShrinkRequest");
Object.defineProperty(exports, "CreateCardSmsTemplateShrinkRequest", { enumerable: true, get: function () { return CreateCardSmsTemplateShrinkRequest_1.CreateCardSmsTemplateShrinkRequest; } });
var CreateCardSmsTemplateResponseBody_1 = require("./CreateCardSmsTemplateResponseBody");
Object.defineProperty(exports, "CreateCardSmsTemplateResponseBody", { enumerable: true, get: function () { return CreateCardSmsTemplateResponseBody_1.CreateCardSmsTemplateResponseBody; } });
var CreateCardSmsTemplateResponse_1 = require("./CreateCardSmsTemplateResponse");
Object.defineProperty(exports, "CreateCardSmsTemplateResponse", { enumerable: true, get: function () { return CreateCardSmsTemplateResponse_1.CreateCardSmsTemplateResponse; } });
var CreateSmartShortUrlRequest_1 = require("./CreateSmartShortUrlRequest");
Object.defineProperty(exports, "CreateSmartShortUrlRequest", { enumerable: true, get: function () { return CreateSmartShortUrlRequest_1.CreateSmartShortUrlRequest; } });
var CreateSmartShortUrlResponseBody_1 = require("./CreateSmartShortUrlResponseBody");
Object.defineProperty(exports, "CreateSmartShortUrlResponseBody", { enumerable: true, get: function () { return CreateSmartShortUrlResponseBody_1.CreateSmartShortUrlResponseBody; } });
var CreateSmartShortUrlResponse_1 = require("./CreateSmartShortUrlResponse");
Object.defineProperty(exports, "CreateSmartShortUrlResponse", { enumerable: true, get: function () { return CreateSmartShortUrlResponse_1.CreateSmartShortUrlResponse; } });
var CreateSmsAuthorizationLetterRequest_1 = require("./CreateSmsAuthorizationLetterRequest");
Object.defineProperty(exports, "CreateSmsAuthorizationLetterRequest", { enumerable: true, get: function () { return CreateSmsAuthorizationLetterRequest_1.CreateSmsAuthorizationLetterRequest; } });
var CreateSmsAuthorizationLetterShrinkRequest_1 = require("./CreateSmsAuthorizationLetterShrinkRequest");
Object.defineProperty(exports, "CreateSmsAuthorizationLetterShrinkRequest", { enumerable: true, get: function () { return CreateSmsAuthorizationLetterShrinkRequest_1.CreateSmsAuthorizationLetterShrinkRequest; } });
var CreateSmsAuthorizationLetterResponseBody_1 = require("./CreateSmsAuthorizationLetterResponseBody");
Object.defineProperty(exports, "CreateSmsAuthorizationLetterResponseBody", { enumerable: true, get: function () { return CreateSmsAuthorizationLetterResponseBody_1.CreateSmsAuthorizationLetterResponseBody; } });
var CreateSmsAuthorizationLetterResponse_1 = require("./CreateSmsAuthorizationLetterResponse");
Object.defineProperty(exports, "CreateSmsAuthorizationLetterResponse", { enumerable: true, get: function () { return CreateSmsAuthorizationLetterResponse_1.CreateSmsAuthorizationLetterResponse; } });
var CreateSmsSignRequest_1 = require("./CreateSmsSignRequest");
Object.defineProperty(exports, "CreateSmsSignRequest", { enumerable: true, get: function () { return CreateSmsSignRequest_1.CreateSmsSignRequest; } });
var CreateSmsSignShrinkRequest_1 = require("./CreateSmsSignShrinkRequest");
Object.defineProperty(exports, "CreateSmsSignShrinkRequest", { enumerable: true, get: function () { return CreateSmsSignShrinkRequest_1.CreateSmsSignShrinkRequest; } });
var CreateSmsSignResponseBody_1 = require("./CreateSmsSignResponseBody");
Object.defineProperty(exports, "CreateSmsSignResponseBody", { enumerable: true, get: function () { return CreateSmsSignResponseBody_1.CreateSmsSignResponseBody; } });
var CreateSmsSignResponse_1 = require("./CreateSmsSignResponse");
Object.defineProperty(exports, "CreateSmsSignResponse", { enumerable: true, get: function () { return CreateSmsSignResponse_1.CreateSmsSignResponse; } });
var CreateSmsTemplateRequest_1 = require("./CreateSmsTemplateRequest");
Object.defineProperty(exports, "CreateSmsTemplateRequest", { enumerable: true, get: function () { return CreateSmsTemplateRequest_1.CreateSmsTemplateRequest; } });
var CreateSmsTemplateShrinkRequest_1 = require("./CreateSmsTemplateShrinkRequest");
Object.defineProperty(exports, "CreateSmsTemplateShrinkRequest", { enumerable: true, get: function () { return CreateSmsTemplateShrinkRequest_1.CreateSmsTemplateShrinkRequest; } });
var CreateSmsTemplateResponseBody_1 = require("./CreateSmsTemplateResponseBody");
Object.defineProperty(exports, "CreateSmsTemplateResponseBody", { enumerable: true, get: function () { return CreateSmsTemplateResponseBody_1.CreateSmsTemplateResponseBody; } });
var CreateSmsTemplateResponse_1 = require("./CreateSmsTemplateResponse");
Object.defineProperty(exports, "CreateSmsTemplateResponse", { enumerable: true, get: function () { return CreateSmsTemplateResponse_1.CreateSmsTemplateResponse; } });
var DeleteExtCodeSignRequest_1 = require("./DeleteExtCodeSignRequest");
Object.defineProperty(exports, "DeleteExtCodeSignRequest", { enumerable: true, get: function () { return DeleteExtCodeSignRequest_1.DeleteExtCodeSignRequest; } });
var DeleteExtCodeSignResponseBody_1 = require("./DeleteExtCodeSignResponseBody");
Object.defineProperty(exports, "DeleteExtCodeSignResponseBody", { enumerable: true, get: function () { return DeleteExtCodeSignResponseBody_1.DeleteExtCodeSignResponseBody; } });
var DeleteExtCodeSignResponse_1 = require("./DeleteExtCodeSignResponse");
Object.defineProperty(exports, "DeleteExtCodeSignResponse", { enumerable: true, get: function () { return DeleteExtCodeSignResponse_1.DeleteExtCodeSignResponse; } });
var DeleteShortUrlRequest_1 = require("./DeleteShortUrlRequest");
Object.defineProperty(exports, "DeleteShortUrlRequest", { enumerable: true, get: function () { return DeleteShortUrlRequest_1.DeleteShortUrlRequest; } });
var DeleteShortUrlResponseBody_1 = require("./DeleteShortUrlResponseBody");
Object.defineProperty(exports, "DeleteShortUrlResponseBody", { enumerable: true, get: function () { return DeleteShortUrlResponseBody_1.DeleteShortUrlResponseBody; } });
var DeleteShortUrlResponse_1 = require("./DeleteShortUrlResponse");
Object.defineProperty(exports, "DeleteShortUrlResponse", { enumerable: true, get: function () { return DeleteShortUrlResponse_1.DeleteShortUrlResponse; } });
var DeleteSmsQualificationRequest_1 = require("./DeleteSmsQualificationRequest");
Object.defineProperty(exports, "DeleteSmsQualificationRequest", { enumerable: true, get: function () { return DeleteSmsQualificationRequest_1.DeleteSmsQualificationRequest; } });
var DeleteSmsQualificationResponseBody_1 = require("./DeleteSmsQualificationResponseBody");
Object.defineProperty(exports, "DeleteSmsQualificationResponseBody", { enumerable: true, get: function () { return DeleteSmsQualificationResponseBody_1.DeleteSmsQualificationResponseBody; } });
var DeleteSmsQualificationResponse_1 = require("./DeleteSmsQualificationResponse");
Object.defineProperty(exports, "DeleteSmsQualificationResponse", { enumerable: true, get: function () { return DeleteSmsQualificationResponse_1.DeleteSmsQualificationResponse; } });
var DeleteSmsSignRequest_1 = require("./DeleteSmsSignRequest");
Object.defineProperty(exports, "DeleteSmsSignRequest", { enumerable: true, get: function () { return DeleteSmsSignRequest_1.DeleteSmsSignRequest; } });
var DeleteSmsSignResponseBody_1 = require("./DeleteSmsSignResponseBody");
Object.defineProperty(exports, "DeleteSmsSignResponseBody", { enumerable: true, get: function () { return DeleteSmsSignResponseBody_1.DeleteSmsSignResponseBody; } });
var DeleteSmsSignResponse_1 = require("./DeleteSmsSignResponse");
Object.defineProperty(exports, "DeleteSmsSignResponse", { enumerable: true, get: function () { return DeleteSmsSignResponse_1.DeleteSmsSignResponse; } });
var DeleteSmsTemplateRequest_1 = require("./DeleteSmsTemplateRequest");
Object.defineProperty(exports, "DeleteSmsTemplateRequest", { enumerable: true, get: function () { return DeleteSmsTemplateRequest_1.DeleteSmsTemplateRequest; } });
var DeleteSmsTemplateResponseBody_1 = require("./DeleteSmsTemplateResponseBody");
Object.defineProperty(exports, "DeleteSmsTemplateResponseBody", { enumerable: true, get: function () { return DeleteSmsTemplateResponseBody_1.DeleteSmsTemplateResponseBody; } });
var DeleteSmsTemplateResponse_1 = require("./DeleteSmsTemplateResponse");
Object.defineProperty(exports, "DeleteSmsTemplateResponse", { enumerable: true, get: function () { return DeleteSmsTemplateResponse_1.DeleteSmsTemplateResponse; } });
var GetCardSmsDetailsRequest_1 = require("./GetCardSmsDetailsRequest");
Object.defineProperty(exports, "GetCardSmsDetailsRequest", { enumerable: true, get: function () { return GetCardSmsDetailsRequest_1.GetCardSmsDetailsRequest; } });
var GetCardSmsDetailsResponseBody_1 = require("./GetCardSmsDetailsResponseBody");
Object.defineProperty(exports, "GetCardSmsDetailsResponseBody", { enumerable: true, get: function () { return GetCardSmsDetailsResponseBody_1.GetCardSmsDetailsResponseBody; } });
var GetCardSmsDetailsResponse_1 = require("./GetCardSmsDetailsResponse");
Object.defineProperty(exports, "GetCardSmsDetailsResponse", { enumerable: true, get: function () { return GetCardSmsDetailsResponse_1.GetCardSmsDetailsResponse; } });
var GetCardSmsLinkRequest_1 = require("./GetCardSmsLinkRequest");
Object.defineProperty(exports, "GetCardSmsLinkRequest", { enumerable: true, get: function () { return GetCardSmsLinkRequest_1.GetCardSmsLinkRequest; } });
var GetCardSmsLinkResponseBody_1 = require("./GetCardSmsLinkResponseBody");
Object.defineProperty(exports, "GetCardSmsLinkResponseBody", { enumerable: true, get: function () { return GetCardSmsLinkResponseBody_1.GetCardSmsLinkResponseBody; } });
var GetCardSmsLinkResponse_1 = require("./GetCardSmsLinkResponse");
Object.defineProperty(exports, "GetCardSmsLinkResponse", { enumerable: true, get: function () { return GetCardSmsLinkResponse_1.GetCardSmsLinkResponse; } });
var GetMediaResourceIdRequest_1 = require("./GetMediaResourceIdRequest");
Object.defineProperty(exports, "GetMediaResourceIdRequest", { enumerable: true, get: function () { return GetMediaResourceIdRequest_1.GetMediaResourceIdRequest; } });
var GetMediaResourceIdResponseBody_1 = require("./GetMediaResourceIdResponseBody");
Object.defineProperty(exports, "GetMediaResourceIdResponseBody", { enumerable: true, get: function () { return GetMediaResourceIdResponseBody_1.GetMediaResourceIdResponseBody; } });
var GetMediaResourceIdResponse_1 = require("./GetMediaResourceIdResponse");
Object.defineProperty(exports, "GetMediaResourceIdResponse", { enumerable: true, get: function () { return GetMediaResourceIdResponse_1.GetMediaResourceIdResponse; } });
var GetOssinfoForCardTemplateResponseBody_1 = require("./GetOssinfoForCardTemplateResponseBody");
Object.defineProperty(exports, "GetOSSInfoForCardTemplateResponseBody", { enumerable: true, get: function () { return GetOssinfoForCardTemplateResponseBody_1.GetOSSInfoForCardTemplateResponseBody; } });
var GetOssinfoForCardTemplateResponse_1 = require("./GetOssinfoForCardTemplateResponse");
Object.defineProperty(exports, "GetOSSInfoForCardTemplateResponse", { enumerable: true, get: function () { return GetOssinfoForCardTemplateResponse_1.GetOSSInfoForCardTemplateResponse; } });
var GetOssinfoForUploadFileRequest_1 = require("./GetOssinfoForUploadFileRequest");
Object.defineProperty(exports, "GetOSSInfoForUploadFileRequest", { enumerable: true, get: function () { return GetOssinfoForUploadFileRequest_1.GetOSSInfoForUploadFileRequest; } });
var GetOssinfoForUploadFileResponseBody_1 = require("./GetOssinfoForUploadFileResponseBody");
Object.defineProperty(exports, "GetOSSInfoForUploadFileResponseBody", { enumerable: true, get: function () { return GetOssinfoForUploadFileResponseBody_1.GetOSSInfoForUploadFileResponseBody; } });
var GetOssinfoForUploadFileResponse_1 = require("./GetOssinfoForUploadFileResponse");
Object.defineProperty(exports, "GetOSSInfoForUploadFileResponse", { enumerable: true, get: function () { return GetOssinfoForUploadFileResponse_1.GetOSSInfoForUploadFileResponse; } });
var GetQualificationOssInfoRequest_1 = require("./GetQualificationOssInfoRequest");
Object.defineProperty(exports, "GetQualificationOssInfoRequest", { enumerable: true, get: function () { return GetQualificationOssInfoRequest_1.GetQualificationOssInfoRequest; } });
var GetQualificationOssInfoResponseBody_1 = require("./GetQualificationOssInfoResponseBody");
Object.defineProperty(exports, "GetQualificationOssInfoResponseBody", { enumerable: true, get: function () { return GetQualificationOssInfoResponseBody_1.GetQualificationOssInfoResponseBody; } });
var GetQualificationOssInfoResponse_1 = require("./GetQualificationOssInfoResponse");
Object.defineProperty(exports, "GetQualificationOssInfoResponse", { enumerable: true, get: function () { return GetQualificationOssInfoResponse_1.GetQualificationOssInfoResponse; } });
var GetSmsSignRequest_1 = require("./GetSmsSignRequest");
Object.defineProperty(exports, "GetSmsSignRequest", { enumerable: true, get: function () { return GetSmsSignRequest_1.GetSmsSignRequest; } });
var GetSmsSignResponseBody_1 = require("./GetSmsSignResponseBody");
Object.defineProperty(exports, "GetSmsSignResponseBody", { enumerable: true, get: function () { return GetSmsSignResponseBody_1.GetSmsSignResponseBody; } });
var GetSmsSignResponse_1 = require("./GetSmsSignResponse");
Object.defineProperty(exports, "GetSmsSignResponse", { enumerable: true, get: function () { return GetSmsSignResponse_1.GetSmsSignResponse; } });
var GetSmsTemplateRequest_1 = require("./GetSmsTemplateRequest");
Object.defineProperty(exports, "GetSmsTemplateRequest", { enumerable: true, get: function () { return GetSmsTemplateRequest_1.GetSmsTemplateRequest; } });
var GetSmsTemplateResponseBody_1 = require("./GetSmsTemplateResponseBody");
Object.defineProperty(exports, "GetSmsTemplateResponseBody", { enumerable: true, get: function () { return GetSmsTemplateResponseBody_1.GetSmsTemplateResponseBody; } });
var GetSmsTemplateResponse_1 = require("./GetSmsTemplateResponse");
Object.defineProperty(exports, "GetSmsTemplateResponse", { enumerable: true, get: function () { return GetSmsTemplateResponse_1.GetSmsTemplateResponse; } });
var ListTagResourcesRequest_1 = require("./ListTagResourcesRequest");
Object.defineProperty(exports, "ListTagResourcesRequest", { enumerable: true, get: function () { return ListTagResourcesRequest_1.ListTagResourcesRequest; } });
var ListTagResourcesResponseBody_1 = require("./ListTagResourcesResponseBody");
Object.defineProperty(exports, "ListTagResourcesResponseBody", { enumerable: true, get: function () { return ListTagResourcesResponseBody_1.ListTagResourcesResponseBody; } });
var ListTagResourcesResponse_1 = require("./ListTagResourcesResponse");
Object.defineProperty(exports, "ListTagResourcesResponse", { enumerable: true, get: function () { return ListTagResourcesResponse_1.ListTagResourcesResponse; } });
var ModifySmsSignRequest_1 = require("./ModifySmsSignRequest");
Object.defineProperty(exports, "ModifySmsSignRequest", { enumerable: true, get: function () { return ModifySmsSignRequest_1.ModifySmsSignRequest; } });
var ModifySmsSignResponseBody_1 = require("./ModifySmsSignResponseBody");
Object.defineProperty(exports, "ModifySmsSignResponseBody", { enumerable: true, get: function () { return ModifySmsSignResponseBody_1.ModifySmsSignResponseBody; } });
var ModifySmsSignResponse_1 = require("./ModifySmsSignResponse");
Object.defineProperty(exports, "ModifySmsSignResponse", { enumerable: true, get: function () { return ModifySmsSignResponse_1.ModifySmsSignResponse; } });
var ModifySmsTemplateRequest_1 = require("./ModifySmsTemplateRequest");
Object.defineProperty(exports, "ModifySmsTemplateRequest", { enumerable: true, get: function () { return ModifySmsTemplateRequest_1.ModifySmsTemplateRequest; } });
var ModifySmsTemplateResponseBody_1 = require("./ModifySmsTemplateResponseBody");
Object.defineProperty(exports, "ModifySmsTemplateResponseBody", { enumerable: true, get: function () { return ModifySmsTemplateResponseBody_1.ModifySmsTemplateResponseBody; } });
var ModifySmsTemplateResponse_1 = require("./ModifySmsTemplateResponse");
Object.defineProperty(exports, "ModifySmsTemplateResponse", { enumerable: true, get: function () { return ModifySmsTemplateResponse_1.ModifySmsTemplateResponse; } });
var QueryCardSmsTemplateRequest_1 = require("./QueryCardSmsTemplateRequest");
Object.defineProperty(exports, "QueryCardSmsTemplateRequest", { enumerable: true, get: function () { return QueryCardSmsTemplateRequest_1.QueryCardSmsTemplateRequest; } });
var QueryCardSmsTemplateResponseBody_1 = require("./QueryCardSmsTemplateResponseBody");
Object.defineProperty(exports, "QueryCardSmsTemplateResponseBody", { enumerable: true, get: function () { return QueryCardSmsTemplateResponseBody_1.QueryCardSmsTemplateResponseBody; } });
var QueryCardSmsTemplateResponse_1 = require("./QueryCardSmsTemplateResponse");
Object.defineProperty(exports, "QueryCardSmsTemplateResponse", { enumerable: true, get: function () { return QueryCardSmsTemplateResponse_1.QueryCardSmsTemplateResponse; } });
var QueryCardSmsTemplateReportRequest_1 = require("./QueryCardSmsTemplateReportRequest");
Object.defineProperty(exports, "QueryCardSmsTemplateReportRequest", { enumerable: true, get: function () { return QueryCardSmsTemplateReportRequest_1.QueryCardSmsTemplateReportRequest; } });
var QueryCardSmsTemplateReportResponseBody_1 = require("./QueryCardSmsTemplateReportResponseBody");
Object.defineProperty(exports, "QueryCardSmsTemplateReportResponseBody", { enumerable: true, get: function () { return QueryCardSmsTemplateReportResponseBody_1.QueryCardSmsTemplateReportResponseBody; } });
var QueryCardSmsTemplateReportResponse_1 = require("./QueryCardSmsTemplateReportResponse");
Object.defineProperty(exports, "QueryCardSmsTemplateReportResponse", { enumerable: true, get: function () { return QueryCardSmsTemplateReportResponse_1.QueryCardSmsTemplateReportResponse; } });
var QueryExtCodeSignRequest_1 = require("./QueryExtCodeSignRequest");
Object.defineProperty(exports, "QueryExtCodeSignRequest", { enumerable: true, get: function () { return QueryExtCodeSignRequest_1.QueryExtCodeSignRequest; } });
var QueryExtCodeSignResponseBody_1 = require("./QueryExtCodeSignResponseBody");
Object.defineProperty(exports, "QueryExtCodeSignResponseBody", { enumerable: true, get: function () { return QueryExtCodeSignResponseBody_1.QueryExtCodeSignResponseBody; } });
var QueryExtCodeSignResponse_1 = require("./QueryExtCodeSignResponse");
Object.defineProperty(exports, "QueryExtCodeSignResponse", { enumerable: true, get: function () { return QueryExtCodeSignResponse_1.QueryExtCodeSignResponse; } });
var QueryMobilesCardSupportRequest_1 = require("./QueryMobilesCardSupportRequest");
Object.defineProperty(exports, "QueryMobilesCardSupportRequest", { enumerable: true, get: function () { return QueryMobilesCardSupportRequest_1.QueryMobilesCardSupportRequest; } });
var QueryMobilesCardSupportShrinkRequest_1 = require("./QueryMobilesCardSupportShrinkRequest");
Object.defineProperty(exports, "QueryMobilesCardSupportShrinkRequest", { enumerable: true, get: function () { return QueryMobilesCardSupportShrinkRequest_1.QueryMobilesCardSupportShrinkRequest; } });
var QueryMobilesCardSupportResponseBody_1 = require("./QueryMobilesCardSupportResponseBody");
Object.defineProperty(exports, "QueryMobilesCardSupportResponseBody", { enumerable: true, get: function () { return QueryMobilesCardSupportResponseBody_1.QueryMobilesCardSupportResponseBody; } });
var QueryMobilesCardSupportResponse_1 = require("./QueryMobilesCardSupportResponse");
Object.defineProperty(exports, "QueryMobilesCardSupportResponse", { enumerable: true, get: function () { return QueryMobilesCardSupportResponse_1.QueryMobilesCardSupportResponse; } });
var QueryPageSmartShortUrlLogRequest_1 = require("./QueryPageSmartShortUrlLogRequest");
Object.defineProperty(exports, "QueryPageSmartShortUrlLogRequest", { enumerable: true, get: function () { return QueryPageSmartShortUrlLogRequest_1.QueryPageSmartShortUrlLogRequest; } });
var QueryPageSmartShortUrlLogResponseBody_1 = require("./QueryPageSmartShortUrlLogResponseBody");
Object.defineProperty(exports, "QueryPageSmartShortUrlLogResponseBody", { enumerable: true, get: function () { return QueryPageSmartShortUrlLogResponseBody_1.QueryPageSmartShortUrlLogResponseBody; } });
var QueryPageSmartShortUrlLogResponse_1 = require("./QueryPageSmartShortUrlLogResponse");
Object.defineProperty(exports, "QueryPageSmartShortUrlLogResponse", { enumerable: true, get: function () { return QueryPageSmartShortUrlLogResponse_1.QueryPageSmartShortUrlLogResponse; } });
var QuerySendDetailsRequest_1 = require("./QuerySendDetailsRequest");
Object.defineProperty(exports, "QuerySendDetailsRequest", { enumerable: true, get: function () { return QuerySendDetailsRequest_1.QuerySendDetailsRequest; } });
var QuerySendDetailsResponseBody_1 = require("./QuerySendDetailsResponseBody");
Object.defineProperty(exports, "QuerySendDetailsResponseBody", { enumerable: true, get: function () { return QuerySendDetailsResponseBody_1.QuerySendDetailsResponseBody; } });
var QuerySendDetailsResponse_1 = require("./QuerySendDetailsResponse");
Object.defineProperty(exports, "QuerySendDetailsResponse", { enumerable: true, get: function () { return QuerySendDetailsResponse_1.QuerySendDetailsResponse; } });
var QuerySendStatisticsRequest_1 = require("./QuerySendStatisticsRequest");
Object.defineProperty(exports, "QuerySendStatisticsRequest", { enumerable: true, get: function () { return QuerySendStatisticsRequest_1.QuerySendStatisticsRequest; } });
var QuerySendStatisticsResponseBody_1 = require("./QuerySendStatisticsResponseBody");
Object.defineProperty(exports, "QuerySendStatisticsResponseBody", { enumerable: true, get: function () { return QuerySendStatisticsResponseBody_1.QuerySendStatisticsResponseBody; } });
var QuerySendStatisticsResponse_1 = require("./QuerySendStatisticsResponse");
Object.defineProperty(exports, "QuerySendStatisticsResponse", { enumerable: true, get: function () { return QuerySendStatisticsResponse_1.QuerySendStatisticsResponse; } });
var QueryShortUrlRequest_1 = require("./QueryShortUrlRequest");
Object.defineProperty(exports, "QueryShortUrlRequest", { enumerable: true, get: function () { return QueryShortUrlRequest_1.QueryShortUrlRequest; } });
var QueryShortUrlResponseBody_1 = require("./QueryShortUrlResponseBody");
Object.defineProperty(exports, "QueryShortUrlResponseBody", { enumerable: true, get: function () { return QueryShortUrlResponseBody_1.QueryShortUrlResponseBody; } });
var QueryShortUrlResponse_1 = require("./QueryShortUrlResponse");
Object.defineProperty(exports, "QueryShortUrlResponse", { enumerable: true, get: function () { return QueryShortUrlResponse_1.QueryShortUrlResponse; } });
var QuerySingleSmsQualificationRequest_1 = require("./QuerySingleSmsQualificationRequest");
Object.defineProperty(exports, "QuerySingleSmsQualificationRequest", { enumerable: true, get: function () { return QuerySingleSmsQualificationRequest_1.QuerySingleSmsQualificationRequest; } });
var QuerySingleSmsQualificationResponseBody_1 = require("./QuerySingleSmsQualificationResponseBody");
Object.defineProperty(exports, "QuerySingleSmsQualificationResponseBody", { enumerable: true, get: function () { return QuerySingleSmsQualificationResponseBody_1.QuerySingleSmsQualificationResponseBody; } });
var QuerySingleSmsQualificationResponse_1 = require("./QuerySingleSmsQualificationResponse");
Object.defineProperty(exports, "QuerySingleSmsQualificationResponse", { enumerable: true, get: function () { return QuerySingleSmsQualificationResponse_1.QuerySingleSmsQualificationResponse; } });
var QuerySmsAuthorizationLetterRequest_1 = require("./QuerySmsAuthorizationLetterRequest");
Object.defineProperty(exports, "QuerySmsAuthorizationLetterRequest", { enumerable: true, get: function () { return QuerySmsAuthorizationLetterRequest_1.QuerySmsAuthorizationLetterRequest; } });
var QuerySmsAuthorizationLetterShrinkRequest_1 = require("./QuerySmsAuthorizationLetterShrinkRequest");
Object.defineProperty(exports, "QuerySmsAuthorizationLetterShrinkRequest", { enumerable: true, get: function () { return QuerySmsAuthorizationLetterShrinkRequest_1.QuerySmsAuthorizationLetterShrinkRequest; } });
var QuerySmsAuthorizationLetterResponseBody_1 = require("./QuerySmsAuthorizationLetterResponseBody");
Object.defineProperty(exports, "QuerySmsAuthorizationLetterResponseBody", { enumerable: true, get: function () { return QuerySmsAuthorizationLetterResponseBody_1.QuerySmsAuthorizationLetterResponseBody; } });
var QuerySmsAuthorizationLetterResponse_1 = require("./QuerySmsAuthorizationLetterResponse");
Object.defineProperty(exports, "QuerySmsAuthorizationLetterResponse", { enumerable: true, get: function () { return QuerySmsAuthorizationLetterResponse_1.QuerySmsAuthorizationLetterResponse; } });
var QuerySmsQualificationRecordRequest_1 = require("./QuerySmsQualificationRecordRequest");
Object.defineProperty(exports, "QuerySmsQualificationRecordRequest", { enumerable: true, get: function () { return QuerySmsQualificationRecordRequest_1.QuerySmsQualificationRecordRequest; } });
var QuerySmsQualificationRecordResponseBody_1 = require("./QuerySmsQualificationRecordResponseBody");
Object.defineProperty(exports, "QuerySmsQualificationRecordResponseBody", { enumerable: true, get: function () { return QuerySmsQualificationRecordResponseBody_1.QuerySmsQualificationRecordResponseBody; } });
var QuerySmsQualificationRecordResponse_1 = require("./QuerySmsQualificationRecordResponse");
Object.defineProperty(exports, "QuerySmsQualificationRecordResponse", { enumerable: true, get: function () { return QuerySmsQualificationRecordResponse_1.QuerySmsQualificationRecordResponse; } });
var QuerySmsSignRequest_1 = require("./QuerySmsSignRequest");
Object.defineProperty(exports, "QuerySmsSignRequest", { enumerable: true, get: function () { return QuerySmsSignRequest_1.QuerySmsSignRequest; } });
var QuerySmsSignResponseBody_1 = require("./QuerySmsSignResponseBody");
Object.defineProperty(exports, "QuerySmsSignResponseBody", { enumerable: true, get: function () { return QuerySmsSignResponseBody_1.QuerySmsSignResponseBody; } });
var QuerySmsSignResponse_1 = require("./QuerySmsSignResponse");
Object.defineProperty(exports, "QuerySmsSignResponse", { enumerable: true, get: function () { return QuerySmsSignResponse_1.QuerySmsSignResponse; } });
var QuerySmsSignListRequest_1 = require("./QuerySmsSignListRequest");
Object.defineProperty(exports, "QuerySmsSignListRequest", { enumerable: true, get: function () { return QuerySmsSignListRequest_1.QuerySmsSignListRequest; } });
var QuerySmsSignListResponseBody_1 = require("./QuerySmsSignListResponseBody");
Object.defineProperty(exports, "QuerySmsSignListResponseBody", { enumerable: true, get: function () { return QuerySmsSignListResponseBody_1.QuerySmsSignListResponseBody; } });
var QuerySmsSignListResponse_1 = require("./QuerySmsSignListResponse");
Object.defineProperty(exports, "QuerySmsSignListResponse", { enumerable: true, get: function () { return QuerySmsSignListResponse_1.QuerySmsSignListResponse; } });
var QuerySmsTemplateRequest_1 = require("./QuerySmsTemplateRequest");
Object.defineProperty(exports, "QuerySmsTemplateRequest", { enumerable: true, get: function () { return QuerySmsTemplateRequest_1.QuerySmsTemplateRequest; } });
var QuerySmsTemplateResponseBody_1 = require("./QuerySmsTemplateResponseBody");
Object.defineProperty(exports, "QuerySmsTemplateResponseBody", { enumerable: true, get: function () { return QuerySmsTemplateResponseBody_1.QuerySmsTemplateResponseBody; } });
var QuerySmsTemplateResponse_1 = require("./QuerySmsTemplateResponse");
Object.defineProperty(exports, "QuerySmsTemplateResponse", { enumerable: true, get: function () { return QuerySmsTemplateResponse_1.QuerySmsTemplateResponse; } });
var QuerySmsTemplateListRequest_1 = require("./QuerySmsTemplateListRequest");
Object.defineProperty(exports, "QuerySmsTemplateListRequest", { enumerable: true, get: function () { return QuerySmsTemplateListRequest_1.QuerySmsTemplateListRequest; } });
var QuerySmsTemplateListResponseBody_1 = require("./QuerySmsTemplateListResponseBody");
Object.defineProperty(exports, "QuerySmsTemplateListResponseBody", { enumerable: true, get: function () { return QuerySmsTemplateListResponseBody_1.QuerySmsTemplateListResponseBody; } });
var QuerySmsTemplateListResponse_1 = require("./QuerySmsTemplateListResponse");
Object.defineProperty(exports, "QuerySmsTemplateListResponse", { enumerable: true, get: function () { return QuerySmsTemplateListResponse_1.QuerySmsTemplateListResponse; } });
var RequiredPhoneCodeRequest_1 = require("./RequiredPhoneCodeRequest");
Object.defineProperty(exports, "RequiredPhoneCodeRequest", { enumerable: true, get: function () { return RequiredPhoneCodeRequest_1.RequiredPhoneCodeRequest; } });
var RequiredPhoneCodeResponseBody_1 = require("./RequiredPhoneCodeResponseBody");
Object.defineProperty(exports, "RequiredPhoneCodeResponseBody", { enumerable: true, get: function () { return RequiredPhoneCodeResponseBody_1.RequiredPhoneCodeResponseBody; } });
var RequiredPhoneCodeResponse_1 = require("./RequiredPhoneCodeResponse");
Object.defineProperty(exports, "RequiredPhoneCodeResponse", { enumerable: true, get: function () { return RequiredPhoneCodeResponse_1.RequiredPhoneCodeResponse; } });
var SendBatchCardSmsRequest_1 = require("./SendBatchCardSmsRequest");
Object.defineProperty(exports, "SendBatchCardSmsRequest", { enumerable: true, get: function () { return SendBatchCardSmsRequest_1.SendBatchCardSmsRequest; } });
var SendBatchCardSmsResponseBody_1 = require("./SendBatchCardSmsResponseBody");
Object.defineProperty(exports, "SendBatchCardSmsResponseBody", { enumerable: true, get: function () { return SendBatchCardSmsResponseBody_1.SendBatchCardSmsResponseBody; } });
var SendBatchCardSmsResponse_1 = require("./SendBatchCardSmsResponse");
Object.defineProperty(exports, "SendBatchCardSmsResponse", { enumerable: true, get: function () { return SendBatchCardSmsResponse_1.SendBatchCardSmsResponse; } });
var SendBatchSmsRequest_1 = require("./SendBatchSmsRequest");
Object.defineProperty(exports, "SendBatchSmsRequest", { enumerable: true, get: function () { return SendBatchSmsRequest_1.SendBatchSmsRequest; } });
var SendBatchSmsResponseBody_1 = require("./SendBatchSmsResponseBody");
Object.defineProperty(exports, "SendBatchSmsResponseBody", { enumerable: true, get: function () { return SendBatchSmsResponseBody_1.SendBatchSmsResponseBody; } });
var SendBatchSmsResponse_1 = require("./SendBatchSmsResponse");
Object.defineProperty(exports, "SendBatchSmsResponse", { enumerable: true, get: function () { return SendBatchSmsResponse_1.SendBatchSmsResponse; } });
var SendCardSmsRequest_1 = require("./SendCardSmsRequest");
Object.defineProperty(exports, "SendCardSmsRequest", { enumerable: true, get: function () { return SendCardSmsRequest_1.SendCardSmsRequest; } });
var SendCardSmsResponseBody_1 = require("./SendCardSmsResponseBody");
Object.defineProperty(exports, "SendCardSmsResponseBody", { enumerable: true, get: function () { return SendCardSmsResponseBody_1.SendCardSmsResponseBody; } });
var SendCardSmsResponse_1 = require("./SendCardSmsResponse");
Object.defineProperty(exports, "SendCardSmsResponse", { enumerable: true, get: function () { return SendCardSmsResponse_1.SendCardSmsResponse; } });
var SendSmsRequest_1 = require("./SendSmsRequest");
Object.defineProperty(exports, "SendSmsRequest", { enumerable: true, get: function () { return SendSmsRequest_1.SendSmsRequest; } });
var SendSmsResponseBody_1 = require("./SendSmsResponseBody");
Object.defineProperty(exports, "SendSmsResponseBody", { enumerable: true, get: function () { return SendSmsResponseBody_1.SendSmsResponseBody; } });
var SendSmsResponse_1 = require("./SendSmsResponse");
Object.defineProperty(exports, "SendSmsResponse", { enumerable: true, get: function () { return SendSmsResponse_1.SendSmsResponse; } });
var SmsConversionIntlRequest_1 = require("./SmsConversionIntlRequest");
Object.defineProperty(exports, "SmsConversionIntlRequest", { enumerable: true, get: function () { return SmsConversionIntlRequest_1.SmsConversionIntlRequest; } });
var SmsConversionIntlResponseBody_1 = require("./SmsConversionIntlResponseBody");
Object.defineProperty(exports, "SmsConversionIntlResponseBody", { enumerable: true, get: function () { return SmsConversionIntlResponseBody_1.SmsConversionIntlResponseBody; } });
var SmsConversionIntlResponse_1 = require("./SmsConversionIntlResponse");
Object.defineProperty(exports, "SmsConversionIntlResponse", { enumerable: true, get: function () { return SmsConversionIntlResponse_1.SmsConversionIntlResponse; } });
var SubmitSmsQualificationRequest_1 = require("./SubmitSmsQualificationRequest");
Object.defineProperty(exports, "SubmitSmsQualificationRequest", { enumerable: true, get: function () { return SubmitSmsQualificationRequest_1.SubmitSmsQualificationRequest; } });
var SubmitSmsQualificationShrinkRequest_1 = require("./SubmitSmsQualificationShrinkRequest");
Object.defineProperty(exports, "SubmitSmsQualificationShrinkRequest", { enumerable: true, get: function () { return SubmitSmsQualificationShrinkRequest_1.SubmitSmsQualificationShrinkRequest; } });
var SubmitSmsQualificationResponseBody_1 = require("./SubmitSmsQualificationResponseBody");
Object.defineProperty(exports, "SubmitSmsQualificationResponseBody", { enumerable: true, get: function () { return SubmitSmsQualificationResponseBody_1.SubmitSmsQualificationResponseBody; } });
var SubmitSmsQualificationResponse_1 = require("./SubmitSmsQualificationResponse");
Object.defineProperty(exports, "SubmitSmsQualificationResponse", { enumerable: true, get: function () { return SubmitSmsQualificationResponse_1.SubmitSmsQualificationResponse; } });
var TagResourcesRequest_1 = require("./TagResourcesRequest");
Object.defineProperty(exports, "TagResourcesRequest", { enumerable: true, get: function () { return TagResourcesRequest_1.TagResourcesRequest; } });
var TagResourcesResponseBody_1 = require("./TagResourcesResponseBody");
Object.defineProperty(exports, "TagResourcesResponseBody", { enumerable: true, get: function () { return TagResourcesResponseBody_1.TagResourcesResponseBody; } });
var TagResourcesResponse_1 = require("./TagResourcesResponse");
Object.defineProperty(exports, "TagResourcesResponse", { enumerable: true, get: function () { return TagResourcesResponse_1.TagResourcesResponse; } });
var UntagResourcesRequest_1 = require("./UntagResourcesRequest");
Object.defineProperty(exports, "UntagResourcesRequest", { enumerable: true, get: function () { return UntagResourcesRequest_1.UntagResourcesRequest; } });
var UntagResourcesResponseBody_1 = require("./UntagResourcesResponseBody");
Object.defineProperty(exports, "UntagResourcesResponseBody", { enumerable: true, get: function () { return UntagResourcesResponseBody_1.UntagResourcesResponseBody; } });
var UntagResourcesResponse_1 = require("./UntagResourcesResponse");
Object.defineProperty(exports, "UntagResourcesResponse", { enumerable: true, get: function () { return UntagResourcesResponse_1.UntagResourcesResponse; } });
var UpdateExtCodeSignRequest_1 = require("./UpdateExtCodeSignRequest");
Object.defineProperty(exports, "UpdateExtCodeSignRequest", { enumerable: true, get: function () { return UpdateExtCodeSignRequest_1.UpdateExtCodeSignRequest; } });
var UpdateExtCodeSignResponseBody_1 = require("./UpdateExtCodeSignResponseBody");
Object.defineProperty(exports, "UpdateExtCodeSignResponseBody", { enumerable: true, get: function () { return UpdateExtCodeSignResponseBody_1.UpdateExtCodeSignResponseBody; } });
var UpdateExtCodeSignResponse_1 = require("./UpdateExtCodeSignResponse");
Object.defineProperty(exports, "UpdateExtCodeSignResponse", { enumerable: true, get: function () { return UpdateExtCodeSignResponse_1.UpdateExtCodeSignResponse; } });
var UpdateSmsQualificationRequest_1 = require("./UpdateSmsQualificationRequest");
Object.defineProperty(exports, "UpdateSmsQualificationRequest", { enumerable: true, get: function () { return UpdateSmsQualificationRequest_1.UpdateSmsQualificationRequest; } });
var UpdateSmsQualificationShrinkRequest_1 = require("./UpdateSmsQualificationShrinkRequest");
Object.defineProperty(exports, "UpdateSmsQualificationShrinkRequest", { enumerable: true, get: function () { return UpdateSmsQualificationShrinkRequest_1.UpdateSmsQualificationShrinkRequest; } });
var UpdateSmsQualificationResponseBody_1 = require("./UpdateSmsQualificationResponseBody");
Object.defineProperty(exports, "UpdateSmsQualificationResponseBody", { enumerable: true, get: function () { return UpdateSmsQualificationResponseBody_1.UpdateSmsQualificationResponseBody; } });
var UpdateSmsQualificationResponse_1 = require("./UpdateSmsQualificationResponse");
Object.defineProperty(exports, "UpdateSmsQualificationResponse", { enumerable: true, get: function () { return UpdateSmsQualificationResponse_1.UpdateSmsQualificationResponse; } });
var UpdateSmsSignRequest_1 = require("./UpdateSmsSignRequest");
Object.defineProperty(exports, "UpdateSmsSignRequest", { enumerable: true, get: function () { return UpdateSmsSignRequest_1.UpdateSmsSignRequest; } });
var UpdateSmsSignShrinkRequest_1 = require("./UpdateSmsSignShrinkRequest");
Object.defineProperty(exports, "UpdateSmsSignShrinkRequest", { enumerable: true, get: function () { return UpdateSmsSignShrinkRequest_1.UpdateSmsSignShrinkRequest; } });
var UpdateSmsSignResponseBody_1 = require("./UpdateSmsSignResponseBody");
Object.defineProperty(exports, "UpdateSmsSignResponseBody", { enumerable: true, get: function () { return UpdateSmsSignResponseBody_1.UpdateSmsSignResponseBody; } });
var UpdateSmsSignResponse_1 = require("./UpdateSmsSignResponse");
Object.defineProperty(exports, "UpdateSmsSignResponse", { enumerable: true, get: function () { return UpdateSmsSignResponse_1.UpdateSmsSignResponse; } });
var UpdateSmsTemplateRequest_1 = require("./UpdateSmsTemplateRequest");
Object.defineProperty(exports, "UpdateSmsTemplateRequest", { enumerable: true, get: function () { return UpdateSmsTemplateRequest_1.UpdateSmsTemplateRequest; } });
var UpdateSmsTemplateShrinkRequest_1 = require("./UpdateSmsTemplateShrinkRequest");
Object.defineProperty(exports, "UpdateSmsTemplateShrinkRequest", { enumerable: true, get: function () { return UpdateSmsTemplateShrinkRequest_1.UpdateSmsTemplateShrinkRequest; } });
var UpdateSmsTemplateResponseBody_1 = require("./UpdateSmsTemplateResponseBody");
Object.defineProperty(exports, "UpdateSmsTemplateResponseBody", { enumerable: true, get: function () { return UpdateSmsTemplateResponseBody_1.UpdateSmsTemplateResponseBody; } });
var UpdateSmsTemplateResponse_1 = require("./UpdateSmsTemplateResponse");
Object.defineProperty(exports, "UpdateSmsTemplateResponse", { enumerable: true, get: function () { return UpdateSmsTemplateResponse_1.UpdateSmsTemplateResponse; } });
var ValidPhoneCodeRequest_1 = require("./ValidPhoneCodeRequest");
Object.defineProperty(exports, "ValidPhoneCodeRequest", { enumerable: true, get: function () { return ValidPhoneCodeRequest_1.ValidPhoneCodeRequest; } });
var ValidPhoneCodeResponseBody_1 = require("./ValidPhoneCodeResponseBody");
Object.defineProperty(exports, "ValidPhoneCodeResponseBody", { enumerable: true, get: function () { return ValidPhoneCodeResponseBody_1.ValidPhoneCodeResponseBody; } });
var ValidPhoneCodeResponse_1 = require("./ValidPhoneCodeResponse");
Object.defineProperty(exports, "ValidPhoneCodeResponse", { enumerable: true, get: function () { return ValidPhoneCodeResponse_1.ValidPhoneCodeResponse; } });
//# sourceMappingURL=model.js.map