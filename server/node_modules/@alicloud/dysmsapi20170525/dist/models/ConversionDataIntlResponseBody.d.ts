import * as $dara from '@darabonba/typescript';
export declare class ConversionDataIntlResponseBody extends $dara.Model {
    /**
     * @remarks
     * The status code. If OK is returned, the request is successful. For more information, see [Error codes](https://help.aliyun.com/document_detail/101346.html?spm=a2c4g.101345.0.0.74326ff2J5EZyt).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The returned message.
     *
     * @example
     * OK
     */
    message?: string;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * F655A8D5-B967-440B-8683-DAD6FF8D****
     */
    requestId?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
