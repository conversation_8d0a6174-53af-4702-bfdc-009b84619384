import * as $dara from '@darabonba/typescript';
import { SendBatchSmsResponseBody } from "./SendBatchSmsResponseBody";
export declare class SendBatchSmsResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: SendBatchSmsResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
