import * as $dara from '@darabonba/typescript';
import { GetCardSmsDetailsResponseBodyCardSendDetailDTORecords } from "./GetCardSmsDetailsResponseBodyCardSendDetailDtorecords";
export declare class GetCardSmsDetailsResponseBodyCardSendDetailDTO extends $dara.Model {
    /**
     * @remarks
     * Current page number
     *
     * @example
     * 1
     */
    currentPage?: number;
    /**
     * @remarks
     * Page size
     *
     * @example
     * 10
     */
    pageSize?: number;
    /**
     * @remarks
     * List of card SMS sending records
     */
    records?: GetCardSmsDetailsResponseBodyCardSendDetailDTORecords[];
    /**
     * @remarks
     * Total count
     *
     * @example
     * 10
     */
    totalCount?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
