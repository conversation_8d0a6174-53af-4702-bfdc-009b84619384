import * as $dara from '@darabonba/typescript';
export declare class GetSmsTemplateResponseBodyAuditInfo extends $dara.Model {
    /**
     * @remarks
     * Audit date and time.
     *
     * @example
     * 2024-06-03 11:20:34
     */
    auditDate?: string;
    /**
     * @remarks
     * Reasons for failed audit.
     *
     * @example
     * 模板内容中包含错别字。
     */
    rejectInfo?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
