import * as $dara from '@darabonba/typescript';
export declare class QueryExtCodeSignRequest extends $dara.Model {
    /**
     * @remarks
     * 扩展码A3
     *
     * @example
     * 01
     */
    extCode?: string;
    ownerId?: number;
    /**
     * @example
     * 1
     */
    pageNo?: number;
    /**
     * @example
     * 20
     */
    pageSize?: number;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * 签名
     *
     * @example
     * 示例值示例值
     */
    signName?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
