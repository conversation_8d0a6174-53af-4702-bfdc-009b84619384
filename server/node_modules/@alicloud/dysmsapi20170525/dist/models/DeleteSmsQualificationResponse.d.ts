import * as $dara from '@darabonba/typescript';
import { DeleteSmsQualificationResponseBody } from "./DeleteSmsQualificationResponseBody";
export declare class DeleteSmsQualificationResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: DeleteSmsQualificationResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
