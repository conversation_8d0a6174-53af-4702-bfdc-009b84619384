import * as $dara from '@darabonba/typescript';
export declare class ValidPhoneCodeRequest extends $dara.Model {
    /**
     * @remarks
     * 验证码
     *
     * This parameter is required.
     *
     * @example
     * 1234
     */
    certifyCode?: string;
    ownerId?: number;
    /**
     * @remarks
     * 手机号
     *
     * This parameter is required.
     *
     * @example
     * 137****1234
     */
    phoneNo?: string;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
