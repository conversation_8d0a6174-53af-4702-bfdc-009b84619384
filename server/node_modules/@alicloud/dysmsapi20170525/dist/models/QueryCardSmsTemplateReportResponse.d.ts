import * as $dara from '@darabonba/typescript';
import { QueryCardSmsTemplateReportResponseBody } from "./QueryCardSmsTemplateReportResponseBody";
export declare class QueryCardSmsTemplateReportResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QueryCardSmsTemplateReportResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
