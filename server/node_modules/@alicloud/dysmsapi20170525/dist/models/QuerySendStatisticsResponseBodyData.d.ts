import * as $dara from '@darabonba/typescript';
import { QuerySendStatisticsResponseBodyDataTargetList } from "./QuerySendStatisticsResponseBodyDataTargetList";
export declare class QuerySendStatisticsResponseBodyData extends $dara.Model {
    /**
     * @remarks
     * The details of the data returned.
     */
    targetList?: QuerySendStatisticsResponseBodyDataTargetList[];
    /**
     * @remarks
     * The total number of entries returned.
     *
     * @example
     * 20
     */
    totalSize?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
