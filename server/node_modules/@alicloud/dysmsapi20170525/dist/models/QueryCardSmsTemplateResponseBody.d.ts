import * as $dara from '@darabonba/typescript';
import { QueryCardSmsTemplateResponseBodyData } from "./QueryCardSmsTemplateResponseBodyData";
export declare class QueryCardSmsTemplateResponseBody extends $dara.Model {
    /**
     * @remarks
     * The HTTP status code.
     *
     * *   The value OK indicates that the request was successful.
     * *   For more information about other response codes, see [API error codes](https://help.aliyun.com/document_detail/101346.html).
     *
     * @example
     * OK
     */
    code?: string;
    /**
     * @remarks
     * The data returned.
     */
    data?: QueryCardSmsTemplateResponseBodyData;
    /**
     * @remarks
     * The request ID.
     *
     * @example
     * F655A8D5-B967-440B-8683-DAD6FF8DE990
     */
    requestId?: string;
    /**
     * @remarks
     * Indicates whether the request is successful. Valid values:
     *
     * *   **true**
     * *   **false**
     *
     * @example
     * true
     */
    success?: boolean;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
