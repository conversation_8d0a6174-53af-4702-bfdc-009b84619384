import * as $dara from '@darabonba/typescript';
import { ListTagResourcesRequestTag } from "./ListTagResourcesRequestTag";
export declare class ListTagResourcesRequest extends $dara.Model {
    /**
     * @remarks
     * The token used to query the next page.
     *
     * @example
     * 23432453245
     */
    nextToken?: string;
    ownerId?: number;
    /**
     * @remarks
     * The number of entries per page.
     *
     * @example
     * 20
     */
    pageSize?: number;
    /**
     * @remarks
     * The name of the cloud service. Set the value to **dysms**.
     *
     * @example
     * dysms
     */
    prodCode?: string;
    /**
     * @remarks
     * The region ID. Set the value to **cn-hangzhou**.
     *
     * This parameter is required.
     *
     * @example
     * cn-hangzhou
     */
    regionId?: string;
    /**
     * @remarks
     * The code of the message template. Specify either the Tag or the ResourceId parameter.
     *
     * @example
     * SMS_23423423
     */
    resourceId?: string[];
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @remarks
     * The type of the resource. Set the value to TEMPLATE.
     *
     * This parameter is required.
     *
     * @example
     * TEMPLATE
     */
    resourceType?: string;
    /**
     * @remarks
     * The tag list. Specify either the Tag or the ResourceId parameter. You can specify a maximum of 20 tags.
     */
    tag?: ListTagResourcesRequestTag[];
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
