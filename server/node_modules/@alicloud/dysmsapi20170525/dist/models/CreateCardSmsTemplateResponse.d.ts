import * as $dara from '@darabonba/typescript';
import { CreateCardSmsTemplateResponseBody } from "./CreateCardSmsTemplateResponseBody";
export declare class CreateCardSmsTemplateResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: CreateCardSmsTemplateResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
