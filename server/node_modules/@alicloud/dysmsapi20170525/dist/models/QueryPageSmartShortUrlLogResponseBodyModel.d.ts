import * as $dara from '@darabonba/typescript';
import { QueryPageSmartShortUrlLogResponseBodyModelList } from "./QueryPageSmartShortUrlLogResponseBodyModelList";
export declare class QueryPageSmartShortUrlLogResponseBodyModel extends $dara.Model {
    list?: QueryPageSmartShortUrlLogResponseBodyModelList[];
    /**
     * @example
     * 74
     */
    pageNo?: number;
    /**
     * @example
     * 15
     */
    pageSize?: number;
    /**
     * @example
     * 66
     */
    totalCount?: number;
    /**
     * @example
     * 86
     */
    totalPage?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
