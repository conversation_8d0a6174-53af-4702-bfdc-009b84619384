import * as $dara from '@darabonba/typescript';
import { CreateSmsSignResponseBody } from "./CreateSmsSignResponseBody";
export declare class CreateSmsSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: CreateSmsSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
