import * as $dara from '@darabonba/typescript';
import { QueryExtCodeSignResponseBody } from "./QueryExtCodeSignResponseBody";
export declare class QueryExtCodeSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: QueryExtCodeSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
