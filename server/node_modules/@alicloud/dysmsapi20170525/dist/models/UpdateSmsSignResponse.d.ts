import * as $dara from '@darabonba/typescript';
import { UpdateSmsSignResponseBody } from "./UpdateSmsSignResponseBody";
export declare class UpdateSmsSignResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: UpdateSmsSignResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
