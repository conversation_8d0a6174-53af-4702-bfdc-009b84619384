import * as $dara from '@darabonba/typescript';
import { ChangeSignatureQualificationResponseBody } from "./ChangeSignatureQualificationResponseBody";
export declare class ChangeSignatureQualificationResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: ChangeSignatureQualificationResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
