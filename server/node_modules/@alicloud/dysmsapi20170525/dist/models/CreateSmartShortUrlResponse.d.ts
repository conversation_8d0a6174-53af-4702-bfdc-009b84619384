import * as $dara from '@darabonba/typescript';
import { CreateSmartShortUrlResponseBody } from "./CreateSmartShortUrlResponseBody";
export declare class CreateSmartShortUrlResponse extends $dara.Model {
    headers?: {
        [key: string]: string;
    };
    statusCode?: number;
    body?: CreateSmartShortUrlResponseBody;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
