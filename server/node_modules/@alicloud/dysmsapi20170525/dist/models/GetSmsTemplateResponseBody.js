"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetSmsTemplateResponseBody = void 0;
// This file is auto-generated, don't edit it
const $dara = __importStar(require("@darabonba/typescript"));
const GetSmsTemplateResponseBodyAuditInfo_1 = require("./GetSmsTemplateResponseBodyAuditInfo");
const GetSmsTemplateResponseBodyFileUrlList_1 = require("./GetSmsTemplateResponseBodyFileUrlList");
const GetSmsTemplateResponseBodyMoreDataFileUrlList_1 = require("./GetSmsTemplateResponseBodyMoreDataFileUrlList");
class GetSmsTemplateResponseBody extends $dara.Model {
    static names() {
        return {
            applyScene: 'ApplyScene',
            auditInfo: 'AuditInfo',
            code: 'Code',
            createDate: 'CreateDate',
            fileUrlList: 'FileUrlList',
            intlType: 'IntlType',
            message: 'Message',
            moreDataFileUrlList: 'MoreDataFileUrlList',
            orderId: 'OrderId',
            relatedSignName: 'RelatedSignName',
            remark: 'Remark',
            requestId: 'RequestId',
            templateCode: 'TemplateCode',
            templateContent: 'TemplateContent',
            templateName: 'TemplateName',
            templateStatus: 'TemplateStatus',
            templateTag: 'TemplateTag',
            templateType: 'TemplateType',
            variableAttribute: 'VariableAttribute',
            vendorAuditStatus: 'VendorAuditStatus',
        };
    }
    static types() {
        return {
            applyScene: 'string',
            auditInfo: GetSmsTemplateResponseBodyAuditInfo_1.GetSmsTemplateResponseBodyAuditInfo,
            code: 'string',
            createDate: 'string',
            fileUrlList: GetSmsTemplateResponseBodyFileUrlList_1.GetSmsTemplateResponseBodyFileUrlList,
            intlType: 'number',
            message: 'string',
            moreDataFileUrlList: GetSmsTemplateResponseBodyMoreDataFileUrlList_1.GetSmsTemplateResponseBodyMoreDataFileUrlList,
            orderId: 'string',
            relatedSignName: 'string',
            remark: 'string',
            requestId: 'string',
            templateCode: 'string',
            templateContent: 'string',
            templateName: 'string',
            templateStatus: 'string',
            templateTag: 'number',
            templateType: 'string',
            variableAttribute: 'string',
            vendorAuditStatus: { 'type': 'map', 'keyType': 'string', 'valueType': 'any' },
        };
    }
    validate() {
        if (this.auditInfo && typeof this.auditInfo.validate === 'function') {
            this.auditInfo.validate();
        }
        if (this.fileUrlList && typeof this.fileUrlList.validate === 'function') {
            this.fileUrlList.validate();
        }
        if (this.moreDataFileUrlList && typeof this.moreDataFileUrlList.validate === 'function') {
            this.moreDataFileUrlList.validate();
        }
        if (this.vendorAuditStatus) {
            $dara.Model.validateMap(this.vendorAuditStatus);
        }
        super.validate();
    }
    constructor(map) {
        super(map);
    }
}
exports.GetSmsTemplateResponseBody = GetSmsTemplateResponseBody;
//# sourceMappingURL=GetSmsTemplateResponseBody.js.map