import * as $dara from '@darabonba/typescript';
import { QuerySmsQualificationRecordResponseBodyDataList } from "./QuerySmsQualificationRecordResponseBodyDataList";
export declare class QuerySmsQualificationRecordResponseBodyData extends $dara.Model {
    list?: QuerySmsQualificationRecordResponseBodyDataList[];
    /**
     * @example
     * 1
     */
    pageNo?: number;
    /**
     * @example
     * 20
     */
    pageSize?: number;
    /**
     * @example
     * 25
     */
    total?: number;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
