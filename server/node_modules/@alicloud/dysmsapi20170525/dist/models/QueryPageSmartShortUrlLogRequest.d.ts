import * as $dara from '@darabonba/typescript';
export declare class QueryPageSmartShortUrlLogRequest extends $dara.Model {
    /**
     * @remarks
     * This parameter is required.
     *
     * @example
     * ********
     */
    createDateEnd?: number;
    /**
     * @remarks
     * This parameter is required.
     *
     * @example
     * ********
     */
    createDateStart?: number;
    ownerId?: number;
    /**
     * @remarks
     * This parameter is required.
     *
     * @example
     * 1
     */
    pageNo?: number;
    /**
     * @remarks
     * This parameter is required.
     *
     * @example
     * 10
     */
    pageSize?: number;
    /**
     * @example
     * 1390000****
     */
    phoneNumber?: string;
    resourceOwnerAccount?: string;
    resourceOwnerId?: number;
    /**
     * @example
     * http://ays.cn/****
     */
    shortUrl?: string;
    static names(): {
        [key: string]: string;
    };
    static types(): {
        [key: string]: any;
    };
    validate(): void;
    constructor(map?: {
        [key: string]: any;
    });
}
