import * as $dara from '@darabonba/typescript';
import OpenApi from '@alicloud/openapi-core';
import { $OpenApiUtil } from '@alicloud/openapi-core';
import * as $_model from './models/model';
export * from './models/model';
export default class Client extends OpenApi {
    constructor(config: $OpenApiUtil.Config);
    getEndpoint(productId: string, regionId: string, endpointRule: string, network: string, suffix: string, endpointMap: {
        [key: string]: string;
    }, endpoint: string): string;
    /**
     * 添加验证码签名信息
     *
     * @param request - AddExtCodeSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns AddExtCodeSignResponse
     */
    addExtCodeSignWithOptions(request: $_model.AddExtCodeSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.AddExtCodeSignResponse>;
    /**
     * 添加验证码签名信息
     *
     * @param request - AddExtCodeSignRequest
     * @returns AddExtCodeSignResponse
     */
    addExtCodeSign(request: $_model.AddExtCodeSignRequest): Promise<$_model.AddExtCodeSignResponse>;
    /**
     * Creates a short URL.
     *
     * @remarks
     *   Before you call this operation, you must register the primary domain name of the source URL in the Short Message Service (SMS) console. After the domain name is registered, you can call this operation to create a short URL. For more information, see [Domain name registration](https://help.aliyun.com/document_detail/302325.html#title-mau-zdh-hd0).
     * *   You can create up to 3,000 short URLs within a natural day.
     * *   After a short URL is generated, a security review is required. Generally, the review takes 10 minutes to 2 hours to complete. Before the security review is passed, the short URL cannot be directly accessed.
     *
     * @param request - AddShortUrlRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns AddShortUrlResponse
     */
    addShortUrlWithOptions(request: $_model.AddShortUrlRequest, runtime: $dara.RuntimeOptions): Promise<$_model.AddShortUrlResponse>;
    /**
     * Creates a short URL.
     *
     * @remarks
     *   Before you call this operation, you must register the primary domain name of the source URL in the Short Message Service (SMS) console. After the domain name is registered, you can call this operation to create a short URL. For more information, see [Domain name registration](https://help.aliyun.com/document_detail/302325.html#title-mau-zdh-hd0).
     * *   You can create up to 3,000 short URLs within a natural day.
     * *   After a short URL is generated, a security review is required. Generally, the review takes 10 minutes to 2 hours to complete. Before the security review is passed, the short URL cannot be directly accessed.
     *
     * @param request - AddShortUrlRequest
     * @returns AddShortUrlResponse
     */
    addShortUrl(request: $_model.AddShortUrlRequest): Promise<$_model.AddShortUrlResponse>;
    /**
     * Creates a signature.
     *
     * @remarks
     * You can call the AddSmsSign operation or use the [Short Message Service (SMS) console](https://dysms.console.aliyun.com/dysms.htm#/overview) to create an SMS signature. The signature must comply with the [SMS signature specifications](https://help.aliyun.com/document_detail/108076.html). You can call the QuerySmsSign operation or use the SMS console to query the review status of the signature.
     * For more information, see [Usage notes](https://help.aliyun.com/document_detail/55324.html).
     * ### QPS limit
     * You can call this operation only once per second. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     * >
     * *   You cannot cancel the review of a signature.
     * *   Individual users can create only one verification code signature, and can create only one general-purpose signature within a natural day. If you need to apply for multiple signatures, we recommend that you upgrade your account to an enterprise user.
     * *   If you need to use the same signature for messages sent to recipients both in and outside the Chinese mainland, the signature must be a general-purpose signature.
     * *   If you apply for a signature or message template, you must specify the signature scenario or template type. You must also provide the information of your services, such as a website URL, a domain name with an ICP filing, an application download URL, or the name of your WeChat official account or mini program. For sign-in scenarios, you must also provide an account and password for tests. A detailed description can improve the review efficiency of signatures and templates.
     * *   An SMS signature must undergo a thorough review process before it can be approved for use.
     *
     * @param request - AddSmsSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns AddSmsSignResponse
     */
    addSmsSignWithOptions(request: $_model.AddSmsSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.AddSmsSignResponse>;
    /**
     * Creates a signature.
     *
     * @remarks
     * You can call the AddSmsSign operation or use the [Short Message Service (SMS) console](https://dysms.console.aliyun.com/dysms.htm#/overview) to create an SMS signature. The signature must comply with the [SMS signature specifications](https://help.aliyun.com/document_detail/108076.html). You can call the QuerySmsSign operation or use the SMS console to query the review status of the signature.
     * For more information, see [Usage notes](https://help.aliyun.com/document_detail/55324.html).
     * ### QPS limit
     * You can call this operation only once per second. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     * >
     * *   You cannot cancel the review of a signature.
     * *   Individual users can create only one verification code signature, and can create only one general-purpose signature within a natural day. If you need to apply for multiple signatures, we recommend that you upgrade your account to an enterprise user.
     * *   If you need to use the same signature for messages sent to recipients both in and outside the Chinese mainland, the signature must be a general-purpose signature.
     * *   If you apply for a signature or message template, you must specify the signature scenario or template type. You must also provide the information of your services, such as a website URL, a domain name with an ICP filing, an application download URL, or the name of your WeChat official account or mini program. For sign-in scenarios, you must also provide an account and password for tests. A detailed description can improve the review efficiency of signatures and templates.
     * *   An SMS signature must undergo a thorough review process before it can be approved for use.
     *
     * @param request - AddSmsSignRequest
     * @returns AddSmsSignResponse
     */
    addSmsSign(request: $_model.AddSmsSignRequest): Promise<$_model.AddSmsSignResponse>;
    /**
     * Creates a message template.
     *
     * @remarks
     * You can call the operation or use the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview) to apply for a message template. The template must comply with the [message template specifications](https://help.aliyun.com/document_detail/108253.html). You can call the [QuerySmsTemplate](https://help.aliyun.com/document_detail/419289.html) operation or use the Alibaba Cloud SMS console to check whether the message template is approved.
     * >
     * *   Message templates pending approval can be withdrawn. You can withdraw a message template pending approval on the Message Templates tab in the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview).
     * *   Message templates that have been approved can be deleted, and cannot be modified. You can delete a message template pending approval on the Message Templates tab in the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview).
     * *   If you call the AddSmsTemplate operation, you can apply for a maximum of 100 message templates in a calendar day. After you apply for a message template, we recommend that you wait for at least 30 seconds before you apply for another one. If you use the Alibaba Cloud SMS console, you can apply for an unlimited number of message templates.
     * *   Messages sent to the Chinese mainland and messages sent to countries or regions outside the Chinese mainland use separate message templates. Create message templates based on your needs.
     * *   If you apply for a signature or message template, you must specify the signature scenario or template type. You must also provide the information of your services, such as a website URL, a domain name with an ICP filing, an application download URL, or the name of your WeChat official account or mini program. For sign-in scenarios, you must also provide an account and password for tests. A detailed description can improve the review efficiency of signatures and templates.
     * *   A signature must undergo a thorough review process before it can be approved for use. For more information, see [Usage notes](https://help.aliyun.com/document_detail/55324.html).
     * ### QPS limits
     * You can call this operation up to 1,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @deprecated OpenAPI AddSmsTemplate is deprecated, please use Dysmsapi::2017-05-25::CreateSmsTemplate instead.
     *
     * @param request - AddSmsTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns AddSmsTemplateResponse
     */
    addSmsTemplateWithOptions(request: $_model.AddSmsTemplateRequest, runtime: $dara.RuntimeOptions): Promise<$_model.AddSmsTemplateResponse>;
    /**
     * Creates a message template.
     *
     * @remarks
     * You can call the operation or use the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview) to apply for a message template. The template must comply with the [message template specifications](https://help.aliyun.com/document_detail/108253.html). You can call the [QuerySmsTemplate](https://help.aliyun.com/document_detail/419289.html) operation or use the Alibaba Cloud SMS console to check whether the message template is approved.
     * >
     * *   Message templates pending approval can be withdrawn. You can withdraw a message template pending approval on the Message Templates tab in the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview).
     * *   Message templates that have been approved can be deleted, and cannot be modified. You can delete a message template pending approval on the Message Templates tab in the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview).
     * *   If you call the AddSmsTemplate operation, you can apply for a maximum of 100 message templates in a calendar day. After you apply for a message template, we recommend that you wait for at least 30 seconds before you apply for another one. If you use the Alibaba Cloud SMS console, you can apply for an unlimited number of message templates.
     * *   Messages sent to the Chinese mainland and messages sent to countries or regions outside the Chinese mainland use separate message templates. Create message templates based on your needs.
     * *   If you apply for a signature or message template, you must specify the signature scenario or template type. You must also provide the information of your services, such as a website URL, a domain name with an ICP filing, an application download URL, or the name of your WeChat official account or mini program. For sign-in scenarios, you must also provide an account and password for tests. A detailed description can improve the review efficiency of signatures and templates.
     * *   A signature must undergo a thorough review process before it can be approved for use. For more information, see [Usage notes](https://help.aliyun.com/document_detail/55324.html).
     * ### QPS limits
     * You can call this operation up to 1,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @deprecated OpenAPI AddSmsTemplate is deprecated, please use Dysmsapi::2017-05-25::CreateSmsTemplate instead.
     *
     * @param request - AddSmsTemplateRequest
     * @returns AddSmsTemplateResponse
     */
    addSmsTemplate(request: $_model.AddSmsTemplateRequest): Promise<$_model.AddSmsTemplateResponse>;
    /**
     * 更换签名的资质和授权书
     *
     * @param request - ChangeSignatureQualificationRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns ChangeSignatureQualificationResponse
     */
    changeSignatureQualificationWithOptions(request: $_model.ChangeSignatureQualificationRequest, runtime: $dara.RuntimeOptions): Promise<$_model.ChangeSignatureQualificationResponse>;
    /**
     * 更换签名的资质和授权书
     *
     * @param request - ChangeSignatureQualificationRequest
     * @returns ChangeSignatureQualificationResponse
     */
    changeSignatureQualification(request: $_model.ChangeSignatureQualificationRequest): Promise<$_model.ChangeSignatureQualificationResponse>;
    /**
     * Checks whether a mobile phone number can receive card messages.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 2,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - CheckMobilesCardSupportRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns CheckMobilesCardSupportResponse
     */
    checkMobilesCardSupportWithOptions(request: $_model.CheckMobilesCardSupportRequest, runtime: $dara.RuntimeOptions): Promise<$_model.CheckMobilesCardSupportResponse>;
    /**
     * Checks whether a mobile phone number can receive card messages.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 2,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - CheckMobilesCardSupportRequest
     * @returns CheckMobilesCardSupportResponse
     */
    checkMobilesCardSupport(request: $_model.CheckMobilesCardSupportRequest): Promise<$_model.CheckMobilesCardSupportResponse>;
    /**
     * Sends conversion rate information to Alibaba Cloud SMS.
     *
     * @param request - ConversionDataIntlRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns ConversionDataIntlResponse
     */
    conversionDataIntlWithOptions(request: $_model.ConversionDataIntlRequest, runtime: $dara.RuntimeOptions): Promise<$_model.ConversionDataIntlResponse>;
    /**
     * Sends conversion rate information to Alibaba Cloud SMS.
     *
     * @param request - ConversionDataIntlRequest
     * @returns ConversionDataIntlResponse
     */
    conversionDataIntl(request: $_model.ConversionDataIntlRequest): Promise<$_model.ConversionDataIntlResponse>;
    /**
     * Creates a card message template.
     *
     * @remarks
     *   The CreateCardSmsTemplate operation saves the card message template information, submits it to the mobile phone manufacturer for approval, and returns the message template ID.
     * *   If the type of the message template is not supported or events that are not supported by the mobile phone manufacturer are specified, the template is not submitted. For more information, see [Supported message templates](https://help.aliyun.com/document_detail/434611.html).
     * *   For information about sample card message templates, see [Sample card message templates](https://help.aliyun.com/document_detail/435361.html).
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param tmpReq - CreateCardSmsTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns CreateCardSmsTemplateResponse
     */
    createCardSmsTemplateWithOptions(tmpReq: $_model.CreateCardSmsTemplateRequest, runtime: $dara.RuntimeOptions): Promise<$_model.CreateCardSmsTemplateResponse>;
    /**
     * Creates a card message template.
     *
     * @remarks
     *   The CreateCardSmsTemplate operation saves the card message template information, submits it to the mobile phone manufacturer for approval, and returns the message template ID.
     * *   If the type of the message template is not supported or events that are not supported by the mobile phone manufacturer are specified, the template is not submitted. For more information, see [Supported message templates](https://help.aliyun.com/document_detail/434611.html).
     * *   For information about sample card message templates, see [Sample card message templates](https://help.aliyun.com/document_detail/435361.html).
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - CreateCardSmsTemplateRequest
     * @returns CreateCardSmsTemplateResponse
     */
    createCardSmsTemplate(request: $_model.CreateCardSmsTemplateRequest): Promise<$_model.CreateCardSmsTemplateResponse>;
    /**
     * 创建短链
     *
     * @param request - CreateSmartShortUrlRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns CreateSmartShortUrlResponse
     */
    createSmartShortUrlWithOptions(request: $_model.CreateSmartShortUrlRequest, runtime: $dara.RuntimeOptions): Promise<$_model.CreateSmartShortUrlResponse>;
    /**
     * 创建短链
     *
     * @param request - CreateSmartShortUrlRequest
     * @returns CreateSmartShortUrlResponse
     */
    createSmartShortUrl(request: $_model.CreateSmartShortUrlRequest): Promise<$_model.CreateSmartShortUrlResponse>;
    /**
     * 创建委托授权书
     *
     * @param tmpReq - CreateSmsAuthorizationLetterRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns CreateSmsAuthorizationLetterResponse
     */
    createSmsAuthorizationLetterWithOptions(tmpReq: $_model.CreateSmsAuthorizationLetterRequest, runtime: $dara.RuntimeOptions): Promise<$_model.CreateSmsAuthorizationLetterResponse>;
    /**
     * 创建委托授权书
     *
     * @param request - CreateSmsAuthorizationLetterRequest
     * @returns CreateSmsAuthorizationLetterResponse
     */
    createSmsAuthorizationLetter(request: $_model.CreateSmsAuthorizationLetterRequest): Promise<$_model.CreateSmsAuthorizationLetterResponse>;
    /**
     * Create SMS Signature
     *
     * @remarks
     * - For details about the announcement of changes to the new and original interfaces, see [Announcement on Updates to SMS Service Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Individual authenticated users can apply for one formal signature per natural day under the same Alibaba Cloud account, while enterprise authenticated users have no current restrictions. For details on the differences in rights between individual and enterprise users, please refer to [User Guide](https://help.aliyun.com/zh/sms/user-guide/usage-notes?spm).
     * - Signature information applied through the interface will be synchronized in the SMS service console. For operations related to signatures in the console, see [SMS Signatures](https://help.aliyun.com/zh/sms/user-guide/create-signatures?spm).
     * - After submitting the signature application, you can query the signature review status and details via the [GetSmsSign](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-getsmssign?spm) interface. You can also [Configure Receipt Messages](https://help.aliyun.com/zh/sms/developer-reference/configure-delivery-receipts-1?spm) and obtain signature review status messages through SignSmsReport.
     *
     * @param tmpReq - CreateSmsSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns CreateSmsSignResponse
     */
    createSmsSignWithOptions(tmpReq: $_model.CreateSmsSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.CreateSmsSignResponse>;
    /**
     * Create SMS Signature
     *
     * @remarks
     * - For details about the announcement of changes to the new and original interfaces, see [Announcement on Updates to SMS Service Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Individual authenticated users can apply for one formal signature per natural day under the same Alibaba Cloud account, while enterprise authenticated users have no current restrictions. For details on the differences in rights between individual and enterprise users, please refer to [User Guide](https://help.aliyun.com/zh/sms/user-guide/usage-notes?spm).
     * - Signature information applied through the interface will be synchronized in the SMS service console. For operations related to signatures in the console, see [SMS Signatures](https://help.aliyun.com/zh/sms/user-guide/create-signatures?spm).
     * - After submitting the signature application, you can query the signature review status and details via the [GetSmsSign](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-getsmssign?spm) interface. You can also [Configure Receipt Messages](https://help.aliyun.com/zh/sms/developer-reference/configure-delivery-receipts-1?spm) and obtain signature review status messages through SignSmsReport.
     *
     * @param request - CreateSmsSignRequest
     * @returns CreateSmsSignResponse
     */
    createSmsSign(request: $_model.CreateSmsSignRequest): Promise<$_model.CreateSmsSignResponse>;
    /**
     * Create SMS Template
     *
     * @remarks
     * - For details about the changes of this new interface compared to the original one, please refer to [Announcement on the Update of SMS Service Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - It is recommended to apply for SMS templates via the API with at least a 30-second interval between each request.
     * - The template information applied through the API will be synchronized in the SMS service console. For operations related to templates in the console, please refer to SMS Templates.
     * - After submitting the template application, you can query the audit status and details using the GetSmsTemplate interface. You can also configure delivery receipts to obtain the audit status messages via TemplateSmsReport.
     * - Domestic SMS templates are not interchangeable with international/Hong Kong, Macao, and Taiwan SMS templates. Please apply for templates based on your business scenario.
     * - Only enterprise-verified users can apply for promotional messages and international/Hong Kong, Macao, and Taiwan messages. For differences in rights between personal and enterprise users, please refer to Usage Instructions.
     *
     * @param tmpReq - CreateSmsTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns CreateSmsTemplateResponse
     */
    createSmsTemplateWithOptions(tmpReq: $_model.CreateSmsTemplateRequest, runtime: $dara.RuntimeOptions): Promise<$_model.CreateSmsTemplateResponse>;
    /**
     * Create SMS Template
     *
     * @remarks
     * - For details about the changes of this new interface compared to the original one, please refer to [Announcement on the Update of SMS Service Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - It is recommended to apply for SMS templates via the API with at least a 30-second interval between each request.
     * - The template information applied through the API will be synchronized in the SMS service console. For operations related to templates in the console, please refer to SMS Templates.
     * - After submitting the template application, you can query the audit status and details using the GetSmsTemplate interface. You can also configure delivery receipts to obtain the audit status messages via TemplateSmsReport.
     * - Domestic SMS templates are not interchangeable with international/Hong Kong, Macao, and Taiwan SMS templates. Please apply for templates based on your business scenario.
     * - Only enterprise-verified users can apply for promotional messages and international/Hong Kong, Macao, and Taiwan messages. For differences in rights between personal and enterprise users, please refer to Usage Instructions.
     *
     * @param request - CreateSmsTemplateRequest
     * @returns CreateSmsTemplateResponse
     */
    createSmsTemplate(request: $_model.CreateSmsTemplateRequest): Promise<$_model.CreateSmsTemplateResponse>;
    /**
     * 删除验证码签名
     *
     * @param request - DeleteExtCodeSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns DeleteExtCodeSignResponse
     */
    deleteExtCodeSignWithOptions(request: $_model.DeleteExtCodeSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.DeleteExtCodeSignResponse>;
    /**
     * 删除验证码签名
     *
     * @param request - DeleteExtCodeSignRequest
     * @returns DeleteExtCodeSignResponse
     */
    deleteExtCodeSign(request: $_model.DeleteExtCodeSignRequest): Promise<$_model.DeleteExtCodeSignResponse>;
    /**
     * Deletes a short URL. After you delete a short URL, it cannot be changed to its original state.
     *
     * @remarks
     * ### QPS limits
     * You can call this operation up to 100 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - DeleteShortUrlRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns DeleteShortUrlResponse
     */
    deleteShortUrlWithOptions(request: $_model.DeleteShortUrlRequest, runtime: $dara.RuntimeOptions): Promise<$_model.DeleteShortUrlResponse>;
    /**
     * Deletes a short URL. After you delete a short URL, it cannot be changed to its original state.
     *
     * @remarks
     * ### QPS limits
     * You can call this operation up to 100 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - DeleteShortUrlRequest
     * @returns DeleteShortUrlResponse
     */
    deleteShortUrl(request: $_model.DeleteShortUrlRequest): Promise<$_model.DeleteShortUrlResponse>;
    /**
     * 删除资质对客openAPI
     *
     * @param request - DeleteSmsQualificationRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns DeleteSmsQualificationResponse
     */
    deleteSmsQualificationWithOptions(request: $_model.DeleteSmsQualificationRequest, runtime: $dara.RuntimeOptions): Promise<$_model.DeleteSmsQualificationResponse>;
    /**
     * 删除资质对客openAPI
     *
     * @param request - DeleteSmsQualificationRequest
     * @returns DeleteSmsQualificationResponse
     */
    deleteSmsQualification(request: $_model.DeleteSmsQualificationRequest): Promise<$_model.DeleteSmsQualificationResponse>;
    /**
     * Deletes a signature.
     *
     * @remarks
     *   You cannot delete a signature that has not been approved.
     * *   After you delete a signature, you cannot recover it. Proceed with caution.
     * ### QPS limits
     * You can call this operation up to 1,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - DeleteSmsSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns DeleteSmsSignResponse
     */
    deleteSmsSignWithOptions(request: $_model.DeleteSmsSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.DeleteSmsSignResponse>;
    /**
     * Deletes a signature.
     *
     * @remarks
     *   You cannot delete a signature that has not been approved.
     * *   After you delete a signature, you cannot recover it. Proceed with caution.
     * ### QPS limits
     * You can call this operation up to 1,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - DeleteSmsSignRequest
     * @returns DeleteSmsSignResponse
     */
    deleteSmsSign(request: $_model.DeleteSmsSignRequest): Promise<$_model.DeleteSmsSignResponse>;
    /**
     * Deletes a message template.
     *
     * @remarks
     *   Message templates pending approval can be withdrawn. You can delete a message template pending approval on the Message Templates tab in the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview).
     * *   Message templates that have been approved can be deleted, and cannot be modified. You can delete a message template pending approval on the Message Templates tab in the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview).
     * *   You cannot recover deleted message templates. Proceed with caution.
     * ### QPS limits
     * You can call this operation up to 1,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - DeleteSmsTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns DeleteSmsTemplateResponse
     */
    deleteSmsTemplateWithOptions(request: $_model.DeleteSmsTemplateRequest, runtime: $dara.RuntimeOptions): Promise<$_model.DeleteSmsTemplateResponse>;
    /**
     * Deletes a message template.
     *
     * @remarks
     *   Message templates pending approval can be withdrawn. You can delete a message template pending approval on the Message Templates tab in the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview).
     * *   Message templates that have been approved can be deleted, and cannot be modified. You can delete a message template pending approval on the Message Templates tab in the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview).
     * *   You cannot recover deleted message templates. Proceed with caution.
     * ### QPS limits
     * You can call this operation up to 1,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - DeleteSmsTemplateRequest
     * @returns DeleteSmsTemplateResponse
     */
    deleteSmsTemplate(request: $_model.DeleteSmsTemplateRequest): Promise<$_model.DeleteSmsTemplateResponse>;
    /**
     * Query card sending details
     *
     * @param request - GetCardSmsDetailsRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns GetCardSmsDetailsResponse
     */
    getCardSmsDetailsWithOptions(request: $_model.GetCardSmsDetailsRequest, runtime: $dara.RuntimeOptions): Promise<$_model.GetCardSmsDetailsResponse>;
    /**
     * Query card sending details
     *
     * @param request - GetCardSmsDetailsRequest
     * @returns GetCardSmsDetailsResponse
     */
    getCardSmsDetails(request: $_model.GetCardSmsDetailsRequest): Promise<$_model.GetCardSmsDetailsResponse>;
    /**
     * Queries the short URLs of a card messages template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 1,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - GetCardSmsLinkRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns GetCardSmsLinkResponse
     */
    getCardSmsLinkWithOptions(request: $_model.GetCardSmsLinkRequest, runtime: $dara.RuntimeOptions): Promise<$_model.GetCardSmsLinkResponse>;
    /**
     * Queries the short URLs of a card messages template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 1,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - GetCardSmsLinkRequest
     * @returns GetCardSmsLinkResponse
     */
    getCardSmsLink(request: $_model.GetCardSmsLinkRequest): Promise<$_model.GetCardSmsLinkResponse>;
    /**
     * Converts a resource uploaded to the specified Object Storage Service (OSS) bucket for unified management. Then, a resource ID is returned. You can manage the resource based on the ID.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - GetMediaResourceIdRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns GetMediaResourceIdResponse
     */
    getMediaResourceIdWithOptions(request: $_model.GetMediaResourceIdRequest, runtime: $dara.RuntimeOptions): Promise<$_model.GetMediaResourceIdResponse>;
    /**
     * Converts a resource uploaded to the specified Object Storage Service (OSS) bucket for unified management. Then, a resource ID is returned. You can manage the resource based on the ID.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - GetMediaResourceIdRequest
     * @returns GetMediaResourceIdResponse
     */
    getMediaResourceId(request: $_model.GetMediaResourceIdRequest): Promise<$_model.GetMediaResourceIdResponse>;
    /**
     * Queries the OSS configuration information about card messages.
     *
     * @remarks
     * Resources such as images and videos used for card message templates can be uploaded to Object Storage Service (OSS) buckets for storage. For more information, see [Upload files to OSS](https://help.aliyun.com/document_detail/437303.html).
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - GetOSSInfoForCardTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns GetOSSInfoForCardTemplateResponse
     */
    getOSSInfoForCardTemplateWithOptions(runtime: $dara.RuntimeOptions): Promise<$_model.GetOSSInfoForCardTemplateResponse>;
    /**
     * Queries the OSS configuration information about card messages.
     *
     * @remarks
     * Resources such as images and videos used for card message templates can be uploaded to Object Storage Service (OSS) buckets for storage. For more information, see [Upload files to OSS](https://help.aliyun.com/document_detail/437303.html).
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     * @returns GetOSSInfoForCardTemplateResponse
     */
    getOSSInfoForCardTemplate(): Promise<$_model.GetOSSInfoForCardTemplateResponse>;
    /**
     * SMS File Upload, Get Authorization Info
     *
     * @remarks
     * - When creating signatures or templates, you can upload materials such as login pages with links, backend page screenshots, software copyrights, supplementary agreements, etc. This helps the review personnel understand your business details. If there are multiple materials, they can be combined into one file, supporting png, jpg, jpeg, doc, docx, pdf formats.
     * - For additional materials needed when creating signatures or templates, you can upload them to the OSS file system for storage. For file upload operations, refer to [OSS File Upload](https://help.aliyun.com/zh/sms/upload-files-through-oss).
     *
     * @param request - GetOSSInfoForUploadFileRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns GetOSSInfoForUploadFileResponse
     */
    getOSSInfoForUploadFileWithOptions(request: $_model.GetOSSInfoForUploadFileRequest, runtime: $dara.RuntimeOptions): Promise<$_model.GetOSSInfoForUploadFileResponse>;
    /**
     * SMS File Upload, Get Authorization Info
     *
     * @remarks
     * - When creating signatures or templates, you can upload materials such as login pages with links, backend page screenshots, software copyrights, supplementary agreements, etc. This helps the review personnel understand your business details. If there are multiple materials, they can be combined into one file, supporting png, jpg, jpeg, doc, docx, pdf formats.
     * - For additional materials needed when creating signatures or templates, you can upload them to the OSS file system for storage. For file upload operations, refer to [OSS File Upload](https://help.aliyun.com/zh/sms/upload-files-through-oss).
     *
     * @param request - GetOSSInfoForUploadFileRequest
     * @returns GetOSSInfoForUploadFileResponse
     */
    getOSSInfoForUploadFile(request: $_model.GetOSSInfoForUploadFileRequest): Promise<$_model.GetOSSInfoForUploadFileResponse>;
    /**
     * 上传文件获取oss配置
     *
     * @param request - GetQualificationOssInfoRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns GetQualificationOssInfoResponse
     */
    getQualificationOssInfoWithOptions(request: $_model.GetQualificationOssInfoRequest, runtime: $dara.RuntimeOptions): Promise<$_model.GetQualificationOssInfoResponse>;
    /**
     * 上传文件获取oss配置
     *
     * @param request - GetQualificationOssInfoRequest
     * @returns GetQualificationOssInfoResponse
     */
    getQualificationOssInfo(request: $_model.GetQualificationOssInfoRequest): Promise<$_model.GetQualificationOssInfoResponse>;
    /**
     * Query SMS Signature Details
     *
     * @remarks
     * - For details about the changes of this new interface and the original one, please refer to [Announcement on the Update of SMS Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Review Time: Generally, after submitting the signature, Alibaba Cloud expects to complete the review within 2 hours (Review Business Hours: Monday to Sunday 9:00~21:00, with legal holidays postponed). It is recommended to submit your application before 18:00.
     * - If the signature fails the review, the reason for the failure will be returned. Please refer to [Handling Suggestions for Failed SMS Reviews](https://help.aliyun.com/zh/sms/user-guide/causes-of-application-failures-and-suggestions?spm), invoke the [UpdateSmsSign](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-updatesmssign?spm) API, or modify the unapproved SMS signature on the [Signature Management](https://dysms.console.aliyun.com/domestic/text/sign) page.
     *
     * @param request - GetSmsSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns GetSmsSignResponse
     */
    getSmsSignWithOptions(request: $_model.GetSmsSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.GetSmsSignResponse>;
    /**
     * Query SMS Signature Details
     *
     * @remarks
     * - For details about the changes of this new interface and the original one, please refer to [Announcement on the Update of SMS Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Review Time: Generally, after submitting the signature, Alibaba Cloud expects to complete the review within 2 hours (Review Business Hours: Monday to Sunday 9:00~21:00, with legal holidays postponed). It is recommended to submit your application before 18:00.
     * - If the signature fails the review, the reason for the failure will be returned. Please refer to [Handling Suggestions for Failed SMS Reviews](https://help.aliyun.com/zh/sms/user-guide/causes-of-application-failures-and-suggestions?spm), invoke the [UpdateSmsSign](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-updatesmssign?spm) API, or modify the unapproved SMS signature on the [Signature Management](https://dysms.console.aliyun.com/domestic/text/sign) page.
     *
     * @param request - GetSmsSignRequest
     * @returns GetSmsSignResponse
     */
    getSmsSign(request: $_model.GetSmsSignRequest): Promise<$_model.GetSmsSignResponse>;
    /**
     * Query Text SMS Template Details
     *
     * @remarks
     * - For details about the announcement of changes to the new and original interfaces, see [Announcement on Updates to SMS Service Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Review Time: Under normal circumstances, Alibaba Cloud expects to complete the review within 2 hours after template submission (review working hours: Monday to Sunday 9:00~21:00, with statutory holidays postponed). It is recommended to submit your application before 18:00.
     * - If the template fails the review, the reason for the failure will be returned. Please refer to [Handling Suggestions for Failed SMS Reviews](https://help.aliyun.com/zh/sms/user-guide/causes-of-application-failures-and-suggestions?spm=a2c4g.********.0.0.41fd339f3bPSCQ), invoke the [ModifySmsTemplate](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-modifysmstemplate?spm=a2c4g.********.0.0.5b1f6e8bQloFit) API or modify the SMS template on the [Template Management](https://dysms.console.aliyun.com/domestic/text/template) page.
     * - The current QuerySmsTemplate interface queries the audit details of a single template by template code. The [QuerySmsTemplateList](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-querysmstemplatelist?spm=a2c4g.********.0.0.24086e8bO8cFn4) interface can query the template details of all templates under your current account.
     *
     * @param request - GetSmsTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns GetSmsTemplateResponse
     */
    getSmsTemplateWithOptions(request: $_model.GetSmsTemplateRequest, runtime: $dara.RuntimeOptions): Promise<$_model.GetSmsTemplateResponse>;
    /**
     * Query Text SMS Template Details
     *
     * @remarks
     * - For details about the announcement of changes to the new and original interfaces, see [Announcement on Updates to SMS Service Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Review Time: Under normal circumstances, Alibaba Cloud expects to complete the review within 2 hours after template submission (review working hours: Monday to Sunday 9:00~21:00, with statutory holidays postponed). It is recommended to submit your application before 18:00.
     * - If the template fails the review, the reason for the failure will be returned. Please refer to [Handling Suggestions for Failed SMS Reviews](https://help.aliyun.com/zh/sms/user-guide/causes-of-application-failures-and-suggestions?spm=a2c4g.********.0.0.41fd339f3bPSCQ), invoke the [ModifySmsTemplate](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-modifysmstemplate?spm=a2c4g.********.0.0.5b1f6e8bQloFit) API or modify the SMS template on the [Template Management](https://dysms.console.aliyun.com/domestic/text/template) page.
     * - The current QuerySmsTemplate interface queries the audit details of a single template by template code. The [QuerySmsTemplateList](https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-querysmstemplatelist?spm=a2c4g.********.0.0.24086e8bO8cFn4) interface can query the template details of all templates under your current account.
     *
     * @param request - GetSmsTemplateRequest
     * @returns GetSmsTemplateResponse
     */
    getSmsTemplate(request: $_model.GetSmsTemplateRequest): Promise<$_model.GetSmsTemplateResponse>;
    /**
     * Queries the tags of a message template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 50 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - ListTagResourcesRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns ListTagResourcesResponse
     */
    listTagResourcesWithOptions(request: $_model.ListTagResourcesRequest, runtime: $dara.RuntimeOptions): Promise<$_model.ListTagResourcesResponse>;
    /**
     * Queries the tags of a message template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 50 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - ListTagResourcesRequest
     * @returns ListTagResourcesResponse
     */
    listTagResources(request: $_model.ListTagResourcesRequest): Promise<$_model.ListTagResourcesResponse>;
    /**
     * Modifies a rejected signature and submit it for approval. Signatures that are pending approval or have been approved cannot be modified.
     *
     * @remarks
     * You can call the operation or use the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview) to modify an existing signature and submit the signature for approval. The signature must comply with the [signature specifications](https://help.aliyun.com/document_detail/108076.html).
     * For more information, see [Usage notes](https://help.aliyun.com/document_detail/55324.html).
     * ### QPS limits
     * You can call this operation up to 1,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     * >
     * *   Signatures pending approval cannot be modified.
     * *   You cannot modify a signature after it is approved. If you no longer need the signature, you can delete it.
     * *   If you are an individual user, you cannot apply for a new signature on the same day that your signature is rejected or deleted. We recommend that you modify the rejected signature and submit it again.
     *
     * @param request - ModifySmsSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns ModifySmsSignResponse
     */
    modifySmsSignWithOptions(request: $_model.ModifySmsSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.ModifySmsSignResponse>;
    /**
     * Modifies a rejected signature and submit it for approval. Signatures that are pending approval or have been approved cannot be modified.
     *
     * @remarks
     * You can call the operation or use the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm#/overview) to modify an existing signature and submit the signature for approval. The signature must comply with the [signature specifications](https://help.aliyun.com/document_detail/108076.html).
     * For more information, see [Usage notes](https://help.aliyun.com/document_detail/55324.html).
     * ### QPS limits
     * You can call this operation up to 1,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     * >
     * *   Signatures pending approval cannot be modified.
     * *   You cannot modify a signature after it is approved. If you no longer need the signature, you can delete it.
     * *   If you are an individual user, you cannot apply for a new signature on the same day that your signature is rejected or deleted. We recommend that you modify the rejected signature and submit it again.
     *
     * @param request - ModifySmsSignRequest
     * @returns ModifySmsSignResponse
     */
    modifySmsSign(request: $_model.ModifySmsSignRequest): Promise<$_model.ModifySmsSignResponse>;
    /**
     * Modifies the information of an unapproved message template and submits it for review again.
     *
     * @remarks
     * After you apply for a message template, if the template fails to pass the review, you can call this operation to modify the template and submit the template again. You can call this operation to modify only a template for a specific message type.
     * The template content must comply with the [SMS template specifications](https://help.aliyun.com/document_detail/108253.html).
     * For more information, see [Usage notes](https://help.aliyun.com/document_detail/55324.html).
     * ### QPS limit
     * You can call this operation up to 1,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @deprecated OpenAPI ModifySmsTemplate is deprecated, please use Dysmsapi::2017-05-25::UpdateSmsTemplate instead.
     *
     * @param request - ModifySmsTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns ModifySmsTemplateResponse
     */
    modifySmsTemplateWithOptions(request: $_model.ModifySmsTemplateRequest, runtime: $dara.RuntimeOptions): Promise<$_model.ModifySmsTemplateResponse>;
    /**
     * Modifies the information of an unapproved message template and submits it for review again.
     *
     * @remarks
     * After you apply for a message template, if the template fails to pass the review, you can call this operation to modify the template and submit the template again. You can call this operation to modify only a template for a specific message type.
     * The template content must comply with the [SMS template specifications](https://help.aliyun.com/document_detail/108253.html).
     * For more information, see [Usage notes](https://help.aliyun.com/document_detail/55324.html).
     * ### QPS limit
     * You can call this operation up to 1,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @deprecated OpenAPI ModifySmsTemplate is deprecated, please use Dysmsapi::2017-05-25::UpdateSmsTemplate instead.
     *
     * @param request - ModifySmsTemplateRequest
     * @returns ModifySmsTemplateResponse
     */
    modifySmsTemplate(request: $_model.ModifySmsTemplateRequest): Promise<$_model.ModifySmsTemplateResponse>;
    /**
     * Queries the review status of a message template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QueryCardSmsTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QueryCardSmsTemplateResponse
     */
    queryCardSmsTemplateWithOptions(request: $_model.QueryCardSmsTemplateRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QueryCardSmsTemplateResponse>;
    /**
     * Queries the review status of a message template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QueryCardSmsTemplateRequest
     * @returns QueryCardSmsTemplateResponse
     */
    queryCardSmsTemplate(request: $_model.QueryCardSmsTemplateRequest): Promise<$_model.QueryCardSmsTemplateResponse>;
    /**
     * Queries sent card messages.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QueryCardSmsTemplateReportRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QueryCardSmsTemplateReportResponse
     */
    queryCardSmsTemplateReportWithOptions(request: $_model.QueryCardSmsTemplateReportRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QueryCardSmsTemplateReportResponse>;
    /**
     * Queries sent card messages.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 300 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QueryCardSmsTemplateReportRequest
     * @returns QueryCardSmsTemplateReportResponse
     */
    queryCardSmsTemplateReport(request: $_model.QueryCardSmsTemplateReportRequest): Promise<$_model.QueryCardSmsTemplateReportResponse>;
    /**
     * 查询验证码签名
     *
     * @param request - QueryExtCodeSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QueryExtCodeSignResponse
     */
    queryExtCodeSignWithOptions(request: $_model.QueryExtCodeSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QueryExtCodeSignResponse>;
    /**
     * 查询验证码签名
     *
     * @param request - QueryExtCodeSignRequest
     * @returns QueryExtCodeSignResponse
     */
    queryExtCodeSign(request: $_model.QueryExtCodeSignRequest): Promise<$_model.QueryExtCodeSignResponse>;
    /**
     * Checks whether a mobile phone number can receive card messages.
     *
     * @param tmpReq - QueryMobilesCardSupportRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QueryMobilesCardSupportResponse
     */
    queryMobilesCardSupportWithOptions(tmpReq: $_model.QueryMobilesCardSupportRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QueryMobilesCardSupportResponse>;
    /**
     * Checks whether a mobile phone number can receive card messages.
     *
     * @param request - QueryMobilesCardSupportRequest
     * @returns QueryMobilesCardSupportResponse
     */
    queryMobilesCardSupport(request: $_model.QueryMobilesCardSupportRequest): Promise<$_model.QueryMobilesCardSupportResponse>;
    /**
     * 点击明细查询
     *
     * @param request - QueryPageSmartShortUrlLogRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QueryPageSmartShortUrlLogResponse
     */
    queryPageSmartShortUrlLogWithOptions(request: $_model.QueryPageSmartShortUrlLogRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QueryPageSmartShortUrlLogResponse>;
    /**
     * 点击明细查询
     *
     * @param request - QueryPageSmartShortUrlLogRequest
     * @returns QueryPageSmartShortUrlLogResponse
     */
    queryPageSmartShortUrlLog(request: $_model.QueryPageSmartShortUrlLogRequest): Promise<$_model.QueryPageSmartShortUrlLogResponse>;
    /**
     * Queries the information about a message.
     *
     * @param request - QuerySendDetailsRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QuerySendDetailsResponse
     */
    querySendDetailsWithOptions(request: $_model.QuerySendDetailsRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QuerySendDetailsResponse>;
    /**
     * Queries the information about a message.
     *
     * @param request - QuerySendDetailsRequest
     * @returns QuerySendDetailsResponse
     */
    querySendDetails(request: $_model.QuerySendDetailsRequest): Promise<$_model.QuerySendDetailsResponse>;
    /**
     * Queries message delivery details.
     *
     * @remarks
     * You can call the operation to query message delivery details, including the number of delivered messages, the number of messages with delivery receipts, and the time that a message is sent. If a large number of messages are sent on the specified date, you can specify the number of items displayed on each page and the number of pages to view the details by page.
     * ### QPS limits
     * You can call this operation up to 20 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QuerySendStatisticsRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QuerySendStatisticsResponse
     */
    querySendStatisticsWithOptions(request: $_model.QuerySendStatisticsRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QuerySendStatisticsResponse>;
    /**
     * Queries message delivery details.
     *
     * @remarks
     * You can call the operation to query message delivery details, including the number of delivered messages, the number of messages with delivery receipts, and the time that a message is sent. If a large number of messages are sent on the specified date, you can specify the number of items displayed on each page and the number of pages to view the details by page.
     * ### QPS limits
     * You can call this operation up to 20 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QuerySendStatisticsRequest
     * @returns QuerySendStatisticsResponse
     */
    querySendStatistics(request: $_model.QuerySendStatisticsRequest): Promise<$_model.QuerySendStatisticsResponse>;
    /**
     * Queries the status of a short URL.
     *
     * @remarks
     * ### QPS limits
     * You can call this operation up to 20 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QueryShortUrlRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QueryShortUrlResponse
     */
    queryShortUrlWithOptions(request: $_model.QueryShortUrlRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QueryShortUrlResponse>;
    /**
     * Queries the status of a short URL.
     *
     * @remarks
     * ### QPS limits
     * You can call this operation up to 20 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QueryShortUrlRequest
     * @returns QueryShortUrlResponse
     */
    queryShortUrl(request: $_model.QueryShortUrlRequest): Promise<$_model.QueryShortUrlResponse>;
    /**
     * 查询单个资质详情
     *
     * @param request - QuerySingleSmsQualificationRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QuerySingleSmsQualificationResponse
     */
    querySingleSmsQualificationWithOptions(request: $_model.QuerySingleSmsQualificationRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QuerySingleSmsQualificationResponse>;
    /**
     * 查询单个资质详情
     *
     * @param request - QuerySingleSmsQualificationRequest
     * @returns QuerySingleSmsQualificationResponse
     */
    querySingleSmsQualification(request: $_model.QuerySingleSmsQualificationRequest): Promise<$_model.QuerySingleSmsQualificationResponse>;
    /**
     * 查询委托授权书
     *
     * @param tmpReq - QuerySmsAuthorizationLetterRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QuerySmsAuthorizationLetterResponse
     */
    querySmsAuthorizationLetterWithOptions(tmpReq: $_model.QuerySmsAuthorizationLetterRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QuerySmsAuthorizationLetterResponse>;
    /**
     * 查询委托授权书
     *
     * @param request - QuerySmsAuthorizationLetterRequest
     * @returns QuerySmsAuthorizationLetterResponse
     */
    querySmsAuthorizationLetter(request: $_model.QuerySmsAuthorizationLetterRequest): Promise<$_model.QuerySmsAuthorizationLetterResponse>;
    /**
     * 查询资质审核列表页
     *
     * @param request - QuerySmsQualificationRecordRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QuerySmsQualificationRecordResponse
     */
    querySmsQualificationRecordWithOptions(request: $_model.QuerySmsQualificationRecordRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QuerySmsQualificationRecordResponse>;
    /**
     * 查询资质审核列表页
     *
     * @param request - QuerySmsQualificationRecordRequest
     * @returns QuerySmsQualificationRecordResponse
     */
    querySmsQualificationRecord(request: $_model.QuerySmsQualificationRecordRequest): Promise<$_model.QuerySmsQualificationRecordResponse>;
    /**
     * Queries the status of a signature.
     *
     * @remarks
     * After you apply for an SMS signature, you can query its status by using the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm) or calling the operation. If the signature is rejected, you can modify the signature based on the reason why it is rejected.
     * ### QPS limits
     * You can call this API operation up to 500 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QuerySmsSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QuerySmsSignResponse
     */
    querySmsSignWithOptions(request: $_model.QuerySmsSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QuerySmsSignResponse>;
    /**
     * Queries the status of a signature.
     *
     * @remarks
     * After you apply for an SMS signature, you can query its status by using the [Alibaba Cloud SMS console](https://dysms.console.aliyun.com/dysms.htm) or calling the operation. If the signature is rejected, you can modify the signature based on the reason why it is rejected.
     * ### QPS limits
     * You can call this API operation up to 500 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QuerySmsSignRequest
     * @returns QuerySmsSignResponse
     */
    querySmsSign(request: $_model.QuerySmsSignRequest): Promise<$_model.QuerySmsSignResponse>;
    /**
     * Queries message signatures by page.
     *
     * @remarks
     * You can call this operation to query the details of message signatures, including the name, creation time, and approval status of each signature. If a message template is rejected, the reason is returned. Modify the message signature based on the reason.
     * ### QPS limit
     * You can call this operation up to 10 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QuerySmsSignListRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QuerySmsSignListResponse
     */
    querySmsSignListWithOptions(request: $_model.QuerySmsSignListRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QuerySmsSignListResponse>;
    /**
     * Queries message signatures by page.
     *
     * @remarks
     * You can call this operation to query the details of message signatures, including the name, creation time, and approval status of each signature. If a message template is rejected, the reason is returned. Modify the message signature based on the reason.
     * ### QPS limit
     * You can call this operation up to 10 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QuerySmsSignListRequest
     * @returns QuerySmsSignListResponse
     */
    querySmsSignList(request: $_model.QuerySmsSignListRequest): Promise<$_model.QuerySmsSignListResponse>;
    /**
     * Queries the approval status of a message template.
     *
     * @remarks
     * After you create a message template, you can call this operation to query the approval status of the template. If a message template is rejected, the reason is returned. Modify the message template based on the reason.
     * ### QPS limit
     * You can call this operation up to 5,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @deprecated OpenAPI QuerySmsTemplate is deprecated, please use Dysmsapi::2017-05-25::GetSmsTemplate instead.
     *
     * @param request - QuerySmsTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QuerySmsTemplateResponse
     */
    querySmsTemplateWithOptions(request: $_model.QuerySmsTemplateRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QuerySmsTemplateResponse>;
    /**
     * Queries the approval status of a message template.
     *
     * @remarks
     * After you create a message template, you can call this operation to query the approval status of the template. If a message template is rejected, the reason is returned. Modify the message template based on the reason.
     * ### QPS limit
     * You can call this operation up to 5,000 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @deprecated OpenAPI QuerySmsTemplate is deprecated, please use Dysmsapi::2017-05-25::GetSmsTemplate instead.
     *
     * @param request - QuerySmsTemplateRequest
     * @returns QuerySmsTemplateResponse
     */
    querySmsTemplate(request: $_model.QuerySmsTemplateRequest): Promise<$_model.QuerySmsTemplateResponse>;
    /**
     * Queries message templates.
     *
     * @remarks
     * You can call this operation to query the details of message templates, including the name, creation time, and approval status of each template. If a message template is rejected, the reason is returned. Modify the message template based on the reason.
     * ### QPS limit
     * You can call this operation up to 10 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QuerySmsTemplateListRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns QuerySmsTemplateListResponse
     */
    querySmsTemplateListWithOptions(request: $_model.QuerySmsTemplateListRequest, runtime: $dara.RuntimeOptions): Promise<$_model.QuerySmsTemplateListResponse>;
    /**
     * Queries message templates.
     *
     * @remarks
     * You can call this operation to query the details of message templates, including the name, creation time, and approval status of each template. If a message template is rejected, the reason is returned. Modify the message template based on the reason.
     * ### QPS limit
     * You can call this operation up to 10 times per second per account. If the number of calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - QuerySmsTemplateListRequest
     * @returns QuerySmsTemplateListResponse
     */
    querySmsTemplateList(request: $_model.QuerySmsTemplateListRequest): Promise<$_model.QuerySmsTemplateListResponse>;
    /**
     * 验证手机验证码
     *
     * @param request - RequiredPhoneCodeRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns RequiredPhoneCodeResponse
     */
    requiredPhoneCodeWithOptions(request: $_model.RequiredPhoneCodeRequest, runtime: $dara.RuntimeOptions): Promise<$_model.RequiredPhoneCodeResponse>;
    /**
     * 验证手机验证码
     *
     * @param request - RequiredPhoneCodeRequest
     * @returns RequiredPhoneCodeResponse
     */
    requiredPhoneCode(request: $_model.RequiredPhoneCodeRequest): Promise<$_model.RequiredPhoneCodeResponse>;
    /**
     * Sends multiple card messages at a time.
     *
     * @remarks
     * You can call the operation to send multiple card messages to a maximum of mobile phone numbers at a time. Different signatures and rollback settings can be specified for the mobile phone numbers.
     * ### QPS limit
     * You can call this operation up to 1,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - SendBatchCardSmsRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns SendBatchCardSmsResponse
     */
    sendBatchCardSmsWithOptions(request: $_model.SendBatchCardSmsRequest, runtime: $dara.RuntimeOptions): Promise<$_model.SendBatchCardSmsResponse>;
    /**
     * Sends multiple card messages at a time.
     *
     * @remarks
     * You can call the operation to send multiple card messages to a maximum of mobile phone numbers at a time. Different signatures and rollback settings can be specified for the mobile phone numbers.
     * ### QPS limit
     * You can call this operation up to 1,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - SendBatchCardSmsRequest
     * @returns SendBatchCardSmsResponse
     */
    sendBatchCardSms(request: $_model.SendBatchCardSmsRequest): Promise<$_model.SendBatchCardSmsResponse>;
    /**
     * Uses a single message template and multiple signatures to send messages to multiple recipients.
     *
     * @remarks
     * You can call the operation to send messages to a maximum of 100 recipients at a time.
     *
     * @param request - SendBatchSmsRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns SendBatchSmsResponse
     */
    sendBatchSmsWithOptions(request: $_model.SendBatchSmsRequest, runtime: $dara.RuntimeOptions): Promise<$_model.SendBatchSmsResponse>;
    /**
     * Uses a single message template and multiple signatures to send messages to multiple recipients.
     *
     * @remarks
     * You can call the operation to send messages to a maximum of 100 recipients at a time.
     *
     * @param request - SendBatchSmsRequest
     * @returns SendBatchSmsResponse
     */
    sendBatchSms(request: $_model.SendBatchSmsRequest): Promise<$_model.SendBatchSmsResponse>;
    /**
     * Sends a card message.
     *
     * @remarks
     *   Make sure that the message template that you want to use has been approved. If the mobile phone number of a recipient does not support card messages, the SendCardSms operation allows the rollback feature to ensure successful delivery.
     * *   When you call the SendCardSms operation to send card messages, the operation checks whether the mobile phone numbers of the recipients support card messages. If the mobile phone numbers do not support card messages, you can specify whether to enable rollback. Otherwise, the card message cannot be delivered.
     * ### QPS limit
     * You can call this operation up to 1,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - SendCardSmsRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns SendCardSmsResponse
     */
    sendCardSmsWithOptions(request: $_model.SendCardSmsRequest, runtime: $dara.RuntimeOptions): Promise<$_model.SendCardSmsResponse>;
    /**
     * Sends a card message.
     *
     * @remarks
     *   Make sure that the message template that you want to use has been approved. If the mobile phone number of a recipient does not support card messages, the SendCardSms operation allows the rollback feature to ensure successful delivery.
     * *   When you call the SendCardSms operation to send card messages, the operation checks whether the mobile phone numbers of the recipients support card messages. If the mobile phone numbers do not support card messages, you can specify whether to enable rollback. Otherwise, the card message cannot be delivered.
     * ### QPS limit
     * You can call this operation up to 1,000 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - SendCardSmsRequest
     * @returns SendCardSmsResponse
     */
    sendCardSms(request: $_model.SendCardSmsRequest): Promise<$_model.SendCardSmsResponse>;
    /**
     * Sends a message. Before you call this operation, submit a message signature and message template, and make sure that the signature and template are approved.
     *
     * @remarks
     *   This operation is mainly used to send a single message. In special scenarios, you can send multiple messages with the same content to a maximum of 1,000 mobile numbers. Note that group sending may be delayed.
     * *   To send messages with different signatures and template content to multiple mobile numbers in a single request, call the [SendBatchSms](https://help.aliyun.com/document_detail/102364.html) operation.
     * *   You are charged for using Alibaba Cloud Short Message Service (SMS) based on the amount of messages sent. For more information, see [Pricing](https://www.aliyun.com/price/product#/sms/detail).
     * *   If your verification code signature and general-purpose signature have the same name, the system uses the general-purpose signature to send messages by default.
     *
     * @param request - SendSmsRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns SendSmsResponse
     */
    sendSmsWithOptions(request: $_model.SendSmsRequest, runtime: $dara.RuntimeOptions): Promise<$_model.SendSmsResponse>;
    /**
     * Sends a message. Before you call this operation, submit a message signature and message template, and make sure that the signature and template are approved.
     *
     * @remarks
     *   This operation is mainly used to send a single message. In special scenarios, you can send multiple messages with the same content to a maximum of 1,000 mobile numbers. Note that group sending may be delayed.
     * *   To send messages with different signatures and template content to multiple mobile numbers in a single request, call the [SendBatchSms](https://help.aliyun.com/document_detail/102364.html) operation.
     * *   You are charged for using Alibaba Cloud Short Message Service (SMS) based on the amount of messages sent. For more information, see [Pricing](https://www.aliyun.com/price/product#/sms/detail).
     * *   If your verification code signature and general-purpose signature have the same name, the system uses the general-purpose signature to send messages by default.
     *
     * @param request - SendSmsRequest
     * @returns SendSmsResponse
     */
    sendSms(request: $_model.SendSmsRequest): Promise<$_model.SendSmsResponse>;
    /**
     * Reports the status of an OTP message to Alibaba Cloud SMS.
     *
     * @remarks
     * Metrics:
     * *   Requested OTP messages
     * *   Verified OTP messages
     * An OTP conversion rate is calculated based on the following formula: OTP conversion rate = Number of verified OTP messages/Number of requested OTP messages.
     * > If you call the SmsConversion operation to query OTP conversion rates, your business may be affected. We recommend that you perform the following operations: 1. Call the SmsConversion operation in an asynchronous manner by configuring queues or events. 2. Manually degrade your services or use a circuit breaker to automatically degrade services.
     *
     * @param request - SmsConversionIntlRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns SmsConversionIntlResponse
     */
    smsConversionIntlWithOptions(request: $_model.SmsConversionIntlRequest, runtime: $dara.RuntimeOptions): Promise<$_model.SmsConversionIntlResponse>;
    /**
     * Reports the status of an OTP message to Alibaba Cloud SMS.
     *
     * @remarks
     * Metrics:
     * *   Requested OTP messages
     * *   Verified OTP messages
     * An OTP conversion rate is calculated based on the following formula: OTP conversion rate = Number of verified OTP messages/Number of requested OTP messages.
     * > If you call the SmsConversion operation to query OTP conversion rates, your business may be affected. We recommend that you perform the following operations: 1. Call the SmsConversion operation in an asynchronous manner by configuring queues or events. 2. Manually degrade your services or use a circuit breaker to automatically degrade services.
     *
     * @param request - SmsConversionIntlRequest
     * @returns SmsConversionIntlResponse
     */
    smsConversionIntl(request: $_model.SmsConversionIntlRequest): Promise<$_model.SmsConversionIntlResponse>;
    /**
     * 创建资质对客openAPI
     *
     * @param tmpReq - SubmitSmsQualificationRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns SubmitSmsQualificationResponse
     */
    submitSmsQualificationWithOptions(tmpReq: $_model.SubmitSmsQualificationRequest, runtime: $dara.RuntimeOptions): Promise<$_model.SubmitSmsQualificationResponse>;
    /**
     * 创建资质对客openAPI
     *
     * @param request - SubmitSmsQualificationRequest
     * @returns SubmitSmsQualificationResponse
     */
    submitSmsQualification(request: $_model.SubmitSmsQualificationRequest): Promise<$_model.SubmitSmsQualificationResponse>;
    /**
     * Attaches tags to a message template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 50 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - TagResourcesRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns TagResourcesResponse
     */
    tagResourcesWithOptions(request: $_model.TagResourcesRequest, runtime: $dara.RuntimeOptions): Promise<$_model.TagResourcesResponse>;
    /**
     * Attaches tags to a message template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 50 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - TagResourcesRequest
     * @returns TagResourcesResponse
     */
    tagResources(request: $_model.TagResourcesRequest): Promise<$_model.TagResourcesResponse>;
    /**
     * Deletes tags from a message template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 50 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - UntagResourcesRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns UntagResourcesResponse
     */
    untagResourcesWithOptions(request: $_model.UntagResourcesRequest, runtime: $dara.RuntimeOptions): Promise<$_model.UntagResourcesResponse>;
    /**
     * Deletes tags from a message template.
     *
     * @remarks
     * ### QPS limit
     * You can call this operation up to 50 times per second per account. If the number of the calls per second exceeds the limit, throttling is triggered. As a result, your business may be affected. We recommend that you take note of the limit when you call this operation.
     *
     * @param request - UntagResourcesRequest
     * @returns UntagResourcesResponse
     */
    untagResources(request: $_model.UntagResourcesRequest): Promise<$_model.UntagResourcesResponse>;
    /**
     * 修改验证码签名
     *
     * @param request - UpdateExtCodeSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns UpdateExtCodeSignResponse
     */
    updateExtCodeSignWithOptions(request: $_model.UpdateExtCodeSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.UpdateExtCodeSignResponse>;
    /**
     * 修改验证码签名
     *
     * @param request - UpdateExtCodeSignRequest
     * @returns UpdateExtCodeSignResponse
     */
    updateExtCodeSign(request: $_model.UpdateExtCodeSignRequest): Promise<$_model.UpdateExtCodeSignResponse>;
    /**
     * 修改资质对客openAPI
     *
     * @param tmpReq - UpdateSmsQualificationRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns UpdateSmsQualificationResponse
     */
    updateSmsQualificationWithOptions(tmpReq: $_model.UpdateSmsQualificationRequest, runtime: $dara.RuntimeOptions): Promise<$_model.UpdateSmsQualificationResponse>;
    /**
     * 修改资质对客openAPI
     *
     * @param request - UpdateSmsQualificationRequest
     * @returns UpdateSmsQualificationResponse
     */
    updateSmsQualification(request: $_model.UpdateSmsQualificationRequest): Promise<$_model.UpdateSmsQualificationResponse>;
    /**
     * Update Text SMS Signature
     *
     * @remarks
     * - For details about the changes of this new interface and the original one, please refer to [Announcement on the Update of SMS Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Only signatures that have not passed the review can be modified. Please refer to [Handling Suggestions for Failed SMS Reviews](https://help.aliyun.com/zh/sms/user-guide/causes-of-application-failures-and-suggestions?spm) and call this interface to modify and resubmit for review after modification.
     * - Signature information applied through the interface will be synchronized in the SMS service console. For operations related to signatures in the console, please see [SMS Signatures](https://help.aliyun.com/zh/sms/user-guide/create-signatures?spm).
     *
     * @param tmpReq - UpdateSmsSignRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns UpdateSmsSignResponse
     */
    updateSmsSignWithOptions(tmpReq: $_model.UpdateSmsSignRequest, runtime: $dara.RuntimeOptions): Promise<$_model.UpdateSmsSignResponse>;
    /**
     * Update Text SMS Signature
     *
     * @remarks
     * - For details about the changes of this new interface and the original one, please refer to [Announcement on the Update of SMS Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Only signatures that have not passed the review can be modified. Please refer to [Handling Suggestions for Failed SMS Reviews](https://help.aliyun.com/zh/sms/user-guide/causes-of-application-failures-and-suggestions?spm) and call this interface to modify and resubmit for review after modification.
     * - Signature information applied through the interface will be synchronized in the SMS service console. For operations related to signatures in the console, please see [SMS Signatures](https://help.aliyun.com/zh/sms/user-guide/create-signatures?spm).
     *
     * @param request - UpdateSmsSignRequest
     * @returns UpdateSmsSignResponse
     */
    updateSmsSign(request: $_model.UpdateSmsSignRequest): Promise<$_model.UpdateSmsSignResponse>;
    /**
     * Update Text SMS Template
     *
     * @remarks
     * - For details about the changes of this new interface compared to the original one, please refer to [Announcement on SMS Service Update: Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Only templates that have not passed the review can be modified. Please refer to [Handling Suggestions for Failed SMS Template Reviews](https://help.aliyun.com/zh/sms/user-guide/causes-of-application-failures-and-suggestions?spm=a2c4g.********.0.0.4bf5561ajcFtMQ) and call this interface to modify and resubmit for review.
     * - Modifications made through the interface will be synchronized in the SMS service console. For related operations on templates in the console, see [SMS Templates](https://help.aliyun.com/zh/sms/user-guide/message-templates/?spm=a2c4g.********.0.0.35a947464Itaxp).
     * ### QPS Limit
     * The single-user QPS limit for this interface is 1000 times/second. Exceeding this limit will result in API throttling, which may impact your business. Please make calls reasonably.
     *
     * @param tmpReq - UpdateSmsTemplateRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns UpdateSmsTemplateResponse
     */
    updateSmsTemplateWithOptions(tmpReq: $_model.UpdateSmsTemplateRequest, runtime: $dara.RuntimeOptions): Promise<$_model.UpdateSmsTemplateResponse>;
    /**
     * Update Text SMS Template
     *
     * @remarks
     * - For details about the changes of this new interface compared to the original one, please refer to [Announcement on SMS Service Update: Signature & Template Interfaces](https://help.aliyun.com/zh/sms/product-overview/announcement-on-sms-service-update-signature-template-interface).
     * - Only templates that have not passed the review can be modified. Please refer to [Handling Suggestions for Failed SMS Template Reviews](https://help.aliyun.com/zh/sms/user-guide/causes-of-application-failures-and-suggestions?spm=a2c4g.********.0.0.4bf5561ajcFtMQ) and call this interface to modify and resubmit for review.
     * - Modifications made through the interface will be synchronized in the SMS service console. For related operations on templates in the console, see [SMS Templates](https://help.aliyun.com/zh/sms/user-guide/message-templates/?spm=a2c4g.********.0.0.35a947464Itaxp).
     * ### QPS Limit
     * The single-user QPS limit for this interface is 1000 times/second. Exceeding this limit will result in API throttling, which may impact your business. Please make calls reasonably.
     *
     * @param request - UpdateSmsTemplateRequest
     * @returns UpdateSmsTemplateResponse
     */
    updateSmsTemplate(request: $_model.UpdateSmsTemplateRequest): Promise<$_model.UpdateSmsTemplateResponse>;
    /**
     * 发送手机验证码
     *
     * @param request - ValidPhoneCodeRequest
     * @param runtime - runtime options for this request RuntimeOptions
     * @returns ValidPhoneCodeResponse
     */
    validPhoneCodeWithOptions(request: $_model.ValidPhoneCodeRequest, runtime: $dara.RuntimeOptions): Promise<$_model.ValidPhoneCodeResponse>;
    /**
     * 发送手机验证码
     *
     * @param request - ValidPhoneCodeRequest
     * @returns ValidPhoneCodeResponse
     */
    validPhoneCode(request: $_model.ValidPhoneCodeRequest): Promise<$_model.ValidPhoneCodeResponse>;
}
