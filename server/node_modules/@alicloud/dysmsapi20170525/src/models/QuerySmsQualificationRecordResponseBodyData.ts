// This file is auto-generated, don't edit it
import * as $dara from '@darabonba/typescript';
import { QuerySmsQualificationRecordResponseBodyDataList } from "./QuerySmsQualificationRecordResponseBodyDataList";


export class QuerySmsQualificationRecordResponseBodyData extends $dara.Model {
  list?: QuerySmsQualificationRecordResponseBodyDataList[];
  /**
   * @example
   * 1
   */
  pageNo?: number;
  /**
   * @example
   * 20
   */
  pageSize?: number;
  /**
   * @example
   * 25
   */
  total?: number;
  static names(): { [key: string]: string } {
    return {
      list: 'List',
      pageNo: 'PageNo',
      pageSize: 'PageSize',
      total: 'Total',
    };
  }

  static types(): { [key: string]: any } {
    return {
      list: { 'type': 'array', 'itemType': QuerySmsQualificationRecordResponseBodyDataList },
      pageNo: 'number',
      pageSize: 'number',
      total: 'number',
    };
  }

  validate() {
    if(Array.isArray(this.list)) {
      $dara.Model.validateArray(this.list);
    }
    super.validate();
  }

  constructor(map?: { [key: string]: any }) {
    super(map);
  }
}

